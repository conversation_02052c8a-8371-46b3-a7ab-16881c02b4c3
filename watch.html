<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="3; url=player.html">
    <title>正在跳转到播放器...</title>
    <link rel="manifest" href="manifest.json">
    <link rel="stylesheet" href="css/watch.css">
    <script src="js/watch.js"></script>
</head>
<body>
    <div class="redirect-container">
        <div class="logo-container">
            <svg class="logo-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <h1 class="logo-text">LibreTV</h1>
        </div>
        <div class="loading-animation"></div>
        <div class="redirect-message">正在加载播放器...</div>
        <div id="redirect-status">准备视频数据中，请稍候...</div>
        <p class="redirect-hint">如果页面没有自动跳转，请<a href="player.html" id="manual-redirect">点击这里</a></p>
    </div>
</body>
</html>
