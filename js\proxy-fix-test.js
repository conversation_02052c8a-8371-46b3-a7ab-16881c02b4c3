/**
 * 代理功能问题修复和测试脚本
 * 用于验证和修复已知问题
 */

class ProxyFixTest {
    constructor() {
        this.issues = [];
        this.fixes = [];
    }

    /**
     * 运行所有修复和测试
     */
    async runAllFixes() {
        console.log('🔧 开始运行代理功能修复和测试...');
        
        await this.fixSubscriptionParsing();
        await this.fixArtPlayerToggle();
        await this.fixLoadBalancingToggle();
        await this.testAllFunctionality();
        
        this.generateFixReport();
    }

    /**
     * 修复订阅解析问题
     */
    async fixSubscriptionParsing() {
        console.log('🔧 修复订阅解析问题...');
        
        if (!window.ProxySubscriptionManager) {
            this.issues.push('ProxySubscriptionManager 未初始化');
            return;
        }

        // 测试订阅解析
        const testSubscriptionContent = `# 测试订阅内容
http://proxy1.example.com:8080
https://proxy2.example.com:8443
socks5://proxy3.example.com:1080
proxy4.example.com:3128
# 注释行
invalid-line-should-be-ignored`;

        try {
            const nodes = window.ProxySubscriptionManager.parseSubscriptionContent(testSubscriptionContent);
            console.log('解析结果:', nodes);
            
            if (nodes.length === 0) {
                this.issues.push('订阅内容解析失败 - 无节点');
            } else {
                this.fixes.push(`订阅解析成功 - 解析到 ${nodes.length} 个节点`);
            }
        } catch (error) {
            this.issues.push('订阅解析异常: ' + error.message);
        }

        // 测试添加测试订阅
        try {
            const testSub = await window.ProxySubscriptionManager.addSubscription(
                '测试订阅',
                'data:text/plain;base64,' + btoa(testSubscriptionContent),
                false
            );
            
            if (testSub && testSub.nodes && testSub.nodes.length > 0) {
                this.fixes.push('测试订阅添加成功');
                
                // 清理测试订阅
                window.ProxySubscriptionManager.removeSubscription(testSub.id);
            } else {
                this.issues.push('测试订阅添加失败或无节点');
            }
        } catch (error) {
            this.issues.push('添加测试订阅失败: ' + error.message);
        }
    }

    /**
     * 修复ArtPlayer代理开关问题
     */
    async fixArtPlayerToggle() {
        console.log('🔧 修复ArtPlayer代理开关问题...');
        
        // 检查全局函数是否存在
        if (typeof window.toggleProxyEnabled !== 'function') {
            this.issues.push('toggleProxyEnabled 函数不存在');
        } else {
            this.fixes.push('toggleProxyEnabled 函数已定义');
        }

        if (typeof window.updateProxyToggleDisplay !== 'function') {
            this.issues.push('updateProxyToggleDisplay 函数不存在');
        } else {
            this.fixes.push('updateProxyToggleDisplay 函数已定义');
        }

        // 测试代理开关功能
        if (window.ProxySubscriptionManager) {
            const originalState = window.ProxySubscriptionManager.proxyEnabled;
            
            try {
                // 测试切换
                if (typeof window.toggleProxyEnabled === 'function') {
                    window.toggleProxyEnabled();
                    const newState = window.ProxySubscriptionManager.proxyEnabled;
                    
                    if (newState !== originalState) {
                        this.fixes.push('代理开关功能正常');
                        
                        // 恢复原状态
                        window.toggleProxyEnabled();
                    } else {
                        this.issues.push('代理开关状态未改变');
                    }
                }
            } catch (error) {
                this.issues.push('代理开关测试失败: ' + error.message);
            }
        }

        // 检查ArtPlayer控件
        if (typeof window.art !== 'undefined' && window.art && window.art.controls) {
            const proxyControl = window.art.controls.find(control => control.name === 'proxy-toggle');
            if (proxyControl) {
                this.fixes.push('ArtPlayer代理控件已找到');
            } else {
                this.issues.push('ArtPlayer代理控件未找到');
            }
        } else {
            console.log('ArtPlayer未初始化或不在播放器页面');
        }
    }

    /**
     * 修复负载均衡开关问题
     */
    async fixLoadBalancingToggle() {
        console.log('🔧 修复负载均衡开关问题...');
        
        const toggleElement = document.getElementById('loadBalancingToggle');
        if (!toggleElement) {
            this.issues.push('负载均衡开关元素未找到');
            return;
        }

        // 检查开关结构
        const toggleBg = toggleElement.parentElement.querySelector('.toggle-bg');
        const toggleDot = toggleElement.parentElement.querySelector('.toggle-dot');
        
        if (!toggleBg) {
            this.issues.push('开关背景元素未找到');
        }
        
        if (!toggleDot) {
            this.issues.push('开关圆点元素未找到');
        }

        // 测试开关功能
        if (window.ProxySubscriptionManager) {
            const originalState = window.ProxySubscriptionManager.loadBalancingEnabled;
            
            try {
                // 模拟点击
                toggleElement.checked = !originalState;
                toggleElement.dispatchEvent(new Event('change'));
                
                const newState = window.ProxySubscriptionManager.loadBalancingEnabled;
                if (newState !== originalState) {
                    this.fixes.push('负载均衡开关功能正常');
                    
                    // 恢复原状态
                    toggleElement.checked = originalState;
                    toggleElement.dispatchEvent(new Event('change'));
                } else {
                    this.issues.push('负载均衡开关状态未改变');
                }
            } catch (error) {
                this.issues.push('负载均衡开关测试失败: ' + error.message);
            }
        }

        // 检查CSS样式
        const computedStyle = window.getComputedStyle(toggleBg || document.body);
        if (computedStyle.transition && computedStyle.transition.includes('background-color')) {
            this.fixes.push('开关CSS动画样式正常');
        } else {
            this.issues.push('开关CSS动画样式缺失');
        }
    }

    /**
     * 测试所有功能
     */
    async testAllFunctionality() {
        console.log('🧪 测试所有功能...');
        
        if (!window.ProxySubscriptionManager) {
            this.issues.push('ProxySubscriptionManager 未初始化');
            return;
        }

        const manager = window.ProxySubscriptionManager;
        
        // 测试基本功能
        try {
            const stats = manager.getStats();
            this.fixes.push(`代理统计功能正常 - 总节点: ${stats.totalNodes}, 在线: ${stats.onlineNodes}`);
        } catch (error) {
            this.issues.push('代理统计功能异常: ' + error.message);
        }

        // 测试节点选择
        try {
            const bestNode = manager.getBestNode();
            if (bestNode) {
                this.fixes.push(`节点选择功能正常 - 当前最佳: ${bestNode.name}`);
            } else {
                this.fixes.push('节点选择功能正常 - 无可用节点');
            }
        } catch (error) {
            this.issues.push('节点选择功能异常: ' + error.message);
        }

        // 测试UI更新
        try {
            if (typeof updateProxyStatus === 'function') {
                updateProxyStatus();
                this.fixes.push('UI状态更新功能正常');
            } else {
                this.issues.push('updateProxyStatus 函数未找到');
            }
        } catch (error) {
            this.issues.push('UI状态更新异常: ' + error.message);
        }
    }

    /**
     * 生成修复报告
     */
    generateFixReport() {
        const totalIssues = this.issues.length;
        const totalFixes = this.fixes.length;
        
        console.log('\n=== 代理功能修复报告 ===');
        console.log(`✅ 修复项目: ${totalFixes}`);
        console.log(`❌ 问题项目: ${totalIssues}`);
        
        if (totalFixes > 0) {
            console.log('\n✅ 修复成功的项目:');
            this.fixes.forEach((fix, index) => {
                console.log(`${index + 1}. ${fix}`);
            });
        }
        
        if (totalIssues > 0) {
            console.log('\n❌ 仍存在的问题:');
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue}`);
            });
            
            console.log('\n🔧 建议的解决方案:');
            this.generateSolutions();
        } else {
            console.log('\n🎉 所有功能正常！');
        }
        
        return {
            fixes: this.fixes,
            issues: this.issues,
            success: totalIssues === 0
        };
    }

    /**
     * 生成解决方案建议
     */
    generateSolutions() {
        this.issues.forEach(issue => {
            if (issue.includes('ProxySubscriptionManager 未初始化')) {
                console.log('- 确保 proxy-subscription.js 已正确加载');
            }
            
            if (issue.includes('订阅内容解析失败')) {
                console.log('- 检查订阅URL是否可访问');
                console.log('- 验证订阅内容格式是否正确');
            }
            
            if (issue.includes('toggleProxyEnabled 函数不存在')) {
                console.log('- 确保 player.js 已正确加载');
                console.log('- 检查函数是否正确定义为全局函数');
            }
            
            if (issue.includes('负载均衡开关')) {
                console.log('- 检查HTML结构是否包含正确的开关元素');
                console.log('- 确保CSS样式已正确加载');
            }
            
            if (issue.includes('ArtPlayer')) {
                console.log('- 确保在播放器页面测试ArtPlayer功能');
                console.log('- 检查ArtPlayer是否已正确初始化');
            }
        });
    }
}

// 创建全局实例
window.ProxyFixTest = new ProxyFixTest();

// 添加快捷测试函数
window.runProxyFixes = function() {
    return window.ProxyFixTest.runAllFixes();
};

// 添加单独的问题修复函数
window.fixProxyIssues = function() {
    console.log('🔧 运行代理问题修复...');
    
    // 修复1: 确保开关样式正确应用
    const loadBalancingToggle = document.getElementById('loadBalancingToggle');
    if (loadBalancingToggle && window.ProxySubscriptionManager) {
        const isEnabled = window.ProxySubscriptionManager.loadBalancingEnabled;
        loadBalancingToggle.checked = isEnabled;
        
        if (typeof updateToggleStyle === 'function') {
            updateToggleStyle(loadBalancingToggle, isEnabled);
        }
        
        console.log('✅ 负载均衡开关状态已修复');
    }
    
    // 修复2: 确保代理开关显示正确
    if (typeof window.updateProxyToggleDisplay === 'function' && window.art) {
        setTimeout(() => {
            window.initProxyToggle();
            console.log('✅ ArtPlayer代理开关已修复');
        }, 1000);
    }
    
    // 修复3: 刷新UI状态
    if (typeof updateProxyStatus === 'function') {
        updateProxyStatus();
        console.log('✅ 代理状态显示已刷新');
    }
    
    console.log('🎉 代理问题修复完成！');
};
