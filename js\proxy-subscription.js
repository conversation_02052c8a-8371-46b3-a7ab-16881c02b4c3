/**
 * 代理订阅管理系统
 * 支持订阅管理、节点选择、负载均衡
 */

class ProxySubscriptionManager {
    constructor() {
        this.subscriptions = this.loadSubscriptions();
        this.nodeLatencies = this.loadNodeLatencies();
        this.selectedNode = this.loadSelectedNode();
        this.loadBalancingEnabled = this.loadLoadBalancingEnabled();
        this.proxyEnabled = this.loadProxyEnabled();
        this.currentNodeIndex = 0; // 用于轮询负载均衡
        this.latencyTestInProgress = false;
        
        // 初始化默认订阅
        this.initializeDefaultSubscription();
        
        // 定期测试节点延迟
        this.startLatencyTesting();
    }

    /**
     * 加载订阅列表
     */
    loadSubscriptions() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.localStorageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('加载代理订阅失败:', error);
            return [];
        }
    }

    /**
     * 保存订阅列表
     */
    saveSubscriptions() {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.localStorageKey, JSON.stringify(this.subscriptions));
        } catch (error) {
            console.error('保存代理订阅失败:', error);
        }
    }

    /**
     * 加载节点延迟数据
     */
    loadNodeLatencies() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.nodeLatencyKey);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('加载节点延迟数据失败:', error);
            return {};
        }
    }

    /**
     * 保存节点延迟数据
     */
    saveNodeLatencies() {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.nodeLatencyKey, JSON.stringify(this.nodeLatencies));
        } catch (error) {
            console.error('保存节点延迟数据失败:', error);
        }
    }

    /**
     * 加载选中的节点
     */
    loadSelectedNode() {
        try {
            return localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.selectedNodeKey) || null;
        } catch (error) {
            console.error('加载选中节点失败:', error);
            return null;
        }
    }

    /**
     * 保存选中的节点
     */
    saveSelectedNode(nodeId) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.selectedNodeKey, nodeId);
            this.selectedNode = nodeId;
        } catch (error) {
            console.error('保存选中节点失败:', error);
        }
    }

    /**
     * 加载负载均衡启用状态
     */
    loadLoadBalancingEnabled() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.loadBalancingKey);
            return stored ? JSON.parse(stored) : true; // 默认启用
        } catch (error) {
            console.error('加载负载均衡状态失败:', error);
            return true;
        }
    }

    /**
     * 保存负载均衡启用状态
     */
    saveLoadBalancingEnabled(enabled) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.loadBalancingKey, JSON.stringify(enabled));
            this.loadBalancingEnabled = enabled;
        } catch (error) {
            console.error('保存负载均衡状态失败:', error);
        }
    }

    /**
     * 加载代理启用状态
     */
    loadProxyEnabled() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.proxyEnabledKey);
            return stored ? JSON.parse(stored) : true; // 默认启用
        } catch (error) {
            console.error('加载代理启用状态失败:', error);
            return true;
        }
    }

    /**
     * 保存代理启用状态
     */
    saveProxyEnabled(enabled) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.proxyEnabledKey, JSON.stringify(enabled));
            this.proxyEnabled = enabled;
        } catch (error) {
            console.error('保存代理启用状态失败:', error);
        }
    }

    /**
     * 初始化默认订阅
     */
    async initializeDefaultSubscription() {
        if (this.subscriptions.length === 0) {
            await this.addSubscription(
                '默认订阅',
                PROXY_SUBSCRIPTION_CONFIG.defaultSubscriptionUrl,
                true
            );
        }
    }

    /**
     * 添加订阅
     */
    async addSubscription(name, url, autoUpdate = true) {
        try {
            const subscription = {
                id: Date.now().toString(),
                name: name,
                url: url,
                autoUpdate: autoUpdate,
                nodes: [],
                lastUpdate: null,
                status: 'pending'
            };

            this.subscriptions.push(subscription);
            this.saveSubscriptions();

            // 立即更新订阅
            await this.updateSubscription(subscription.id);
            
            return subscription;
        } catch (error) {
            console.error('添加订阅失败:', error);
            throw error;
        }
    }

    /**
     * 删除订阅
     */
    removeSubscription(subscriptionId) {
        try {
            this.subscriptions = this.subscriptions.filter(sub => sub.id !== subscriptionId);
            this.saveSubscriptions();
            
            // 清理相关的延迟数据
            Object.keys(this.nodeLatencies).forEach(nodeId => {
                if (nodeId.startsWith(subscriptionId + '_')) {
                    delete this.nodeLatencies[nodeId];
                }
            });
            this.saveNodeLatencies();
            
            return true;
        } catch (error) {
            console.error('删除订阅失败:', error);
            return false;
        }
    }

    /**
     * 更新订阅
     */
    async updateSubscription(subscriptionId) {
        try {
            const subscription = this.subscriptions.find(sub => sub.id === subscriptionId);
            if (!subscription) {
                throw new Error('订阅不存在');
            }

            subscription.status = 'updating';
            
            // 获取订阅内容
            const nodes = await this.fetchSubscriptionNodes(subscription.url);
            
            subscription.nodes = nodes;
            subscription.lastUpdate = new Date().toISOString();
            subscription.status = 'active';
            
            this.saveSubscriptions();
            
            // 测试新节点的延迟
            await this.testNodesLatency(nodes);
            
            return subscription;
        } catch (error) {
            console.error('更新订阅失败:', error);
            const subscription = this.subscriptions.find(sub => sub.id === subscriptionId);
            if (subscription) {
                subscription.status = 'error';
                this.saveSubscriptions();
            }
            throw error;
        }
    }

    /**
     * 获取订阅节点
     */
    async fetchSubscriptionNodes(subscriptionUrl) {
        try {
            // 使用现有的代理系统获取订阅内容
            const proxiedUrl = await window.ProxyAuth?.addAuthToProxyUrl ? 
                await window.ProxyAuth.addAuthToProxyUrl(PROXY_URL + encodeURIComponent(subscriptionUrl)) :
                PROXY_URL + encodeURIComponent(subscriptionUrl);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);

            const response = await fetch(proxiedUrl, {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`获取订阅失败: ${response.status}`);
            }

            const content = await response.text();
            return this.parseSubscriptionContent(content);
        } catch (error) {
            console.error('获取订阅节点失败:', error);
            throw error;
        }
    }

    /**
     * 解析订阅内容
     */
    parseSubscriptionContent(content) {
        try {
            const nodes = [];
            const lines = content.split('\n');
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine || trimmedLine.startsWith('#')) {
                    continue;
                }
                
                // 尝试解析不同格式的代理配置
                const node = this.parseProxyLine(trimmedLine);
                if (node) {
                    nodes.push(node);
                }
            }
            
            return nodes;
        } catch (error) {
            console.error('解析订阅内容失败:', error);
            return [];
        }
    }

    /**
     * 解析代理行
     */
    parseProxyLine(line) {
        try {
            // 支持多种格式的代理配置
            // 格式1: http://proxy.example.com:8080
            // 格式2: https://user:<EMAIL>:8080
            // 格式3: socks5://proxy.example.com:1080

            const urlMatch = line.match(/^(https?|socks5):\/\/(.+)/);
            if (urlMatch) {
                const protocol = urlMatch[1];
                const hostPart = urlMatch[2];

                // 解析主机和端口
                const [hostWithAuth, port] = hostPart.split(':');
                const [host, auth] = hostWithAuth.includes('@') ?
                    hostWithAuth.split('@').reverse() : [hostWithAuth, null];

                return {
                    id: `${protocol}_${host}_${port || '80'}`,
                    name: `${protocol.toUpperCase()} ${host}:${port || '80'}`,
                    protocol: protocol,
                    host: host,
                    port: parseInt(port) || (protocol === 'https' ? 443 : 80),
                    auth: auth,
                    url: line,
                    latency: null,
                    status: 'unknown'
                };
            }

            return null;
        } catch (error) {
            console.error('解析代理行失败:', line, error);
            return null;
        }
    }

    /**
     * 测试节点延迟
     */
    async testNodesLatency(nodes) {
        if (this.latencyTestInProgress) {
            return;
        }

        this.latencyTestInProgress = true;

        try {
            const testPromises = nodes.map(node => this.testNodeLatency(node));
            await Promise.allSettled(testPromises);
            this.saveNodeLatencies();
        } catch (error) {
            console.error('测试节点延迟失败:', error);
        } finally {
            this.latencyTestInProgress = false;
        }
    }

    /**
     * 测试单个节点延迟
     */
    async testNodeLatency(node) {
        try {
            const startTime = Date.now();

            // 使用代理节点测试一个简单的请求
            const testUrl = 'https://httpbin.org/get';
            const proxiedUrl = this.buildProxyUrl(node, testUrl);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), PROXY_SUBSCRIPTION_CONFIG.latencyTestTimeout);

            const response = await fetch(proxiedUrl, {
                method: 'GET',
                signal: controller.signal,
                cache: 'no-cache'
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const latency = Date.now() - startTime;
                this.nodeLatencies[node.id] = {
                    latency: latency,
                    lastTest: Date.now(),
                    status: 'online'
                };
                node.latency = latency;
                node.status = 'online';
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            this.nodeLatencies[node.id] = {
                latency: null,
                lastTest: Date.now(),
                status: 'offline',
                error: error.message
            };
            node.latency = null;
            node.status = 'offline';
        }
    }

    /**
     * 构建代理URL
     */
    buildProxyUrl(node, targetUrl) {
        try {
            // 这里需要根据实际的代理实现来构建URL
            // 目前使用现有的代理系统，但记录使用的节点
            console.log('使用代理节点:', node.name, '延迟:', node.latency + 'ms');

            // 在实际部署中，这里应该根据node的配置来构建真正的代理URL
            // 例如: return `${node.url}/proxy/${encodeURIComponent(targetUrl)}`;

            // 目前使用现有的代理系统
            return PROXY_URL + encodeURIComponent(targetUrl);
        } catch (error) {
            console.error('构建代理URL失败:', error);
            return PROXY_URL + encodeURIComponent(targetUrl);
        }
    }

    /**
     * 获取最佳节点
     */
    getBestNode() {
        if (!this.proxyEnabled) {
            return null;
        }

        const allNodes = this.getAllNodes();
        const onlineNodes = allNodes.filter(node =>
            this.nodeLatencies[node.id] &&
            this.nodeLatencies[node.id].status === 'online'
        );

        if (onlineNodes.length === 0) {
            return null;
        }

        if (!this.loadBalancingEnabled) {
            // 如果没有启用负载均衡，返回延迟最低的节点
            return onlineNodes.reduce((best, current) => {
                const bestLatency = this.nodeLatencies[best.id]?.latency || Infinity;
                const currentLatency = this.nodeLatencies[current.id]?.latency || Infinity;
                return currentLatency < bestLatency ? current : best;
            });
        }

        // 负载均衡策略
        const strategy = PROXY_SUBSCRIPTION_CONFIG.defaultStrategy;

        switch (strategy) {
            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.ROUND_ROBIN:
                return this.getRoundRobinNode(onlineNodes);

            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.RANDOM:
                return onlineNodes[Math.floor(Math.random() * onlineNodes.length)];

            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.LEAST_LATENCY:
            default:
                // 选择延迟最低的几个节点中的一个
                const sortedNodes = onlineNodes.sort((a, b) => {
                    const aLatency = this.nodeLatencies[a.id]?.latency || Infinity;
                    const bLatency = this.nodeLatencies[b.id]?.latency || Infinity;
                    return aLatency - bLatency;
                });

                // 从前25%的节点中随机选择一个
                const topCount = Math.max(1, Math.ceil(sortedNodes.length * 0.25));
                const topNodes = sortedNodes.slice(0, topCount);
                return topNodes[Math.floor(Math.random() * topNodes.length)];
        }
    }

    /**
     * 轮询获取节点
     */
    getRoundRobinNode(nodes) {
        if (nodes.length === 0) return null;

        const node = nodes[this.currentNodeIndex % nodes.length];
        this.currentNodeIndex = (this.currentNodeIndex + 1) % nodes.length;
        return node;
    }

    /**
     * 获取所有节点
     */
    getAllNodes() {
        const allNodes = [];
        for (const subscription of this.subscriptions) {
            if (subscription.status === 'active' && subscription.nodes) {
                allNodes.push(...subscription.nodes);
            }
        }
        return allNodes;
    }

    /**
     * 开始延迟测试
     */
    startLatencyTesting() {
        // 立即测试一次
        setTimeout(() => {
            this.testAllNodesLatency();
        }, 1000);

        // 定期测试
        setInterval(() => {
            this.testAllNodesLatency();
        }, PROXY_SUBSCRIPTION_CONFIG.latencyTestInterval);
    }

    /**
     * 测试所有节点延迟
     */
    async testAllNodesLatency() {
        const allNodes = this.getAllNodes();
        if (allNodes.length > 0) {
            await this.testNodesLatency(allNodes);
        }
    }

    /**
     * 获取代理统计信息
     */
    getStats() {
        const allNodes = this.getAllNodes();
        const onlineNodes = allNodes.filter(node =>
            this.nodeLatencies[node.id] &&
            this.nodeLatencies[node.id].status === 'online'
        );

        return {
            totalSubscriptions: this.subscriptions.length,
            totalNodes: allNodes.length,
            onlineNodes: onlineNodes.length,
            offlineNodes: allNodes.length - onlineNodes.length,
            proxyEnabled: this.proxyEnabled,
            loadBalancingEnabled: this.loadBalancingEnabled,
            selectedNode: this.selectedNode,
            bestNode: this.getBestNode()
        };
    }
}

// 创建全局实例
window.ProxySubscriptionManager = new ProxySubscriptionManager();
