/**
 * 代理订阅管理系统
 * 支持订阅管理、节点选择、负载均衡
 */

class ProxySubscriptionManager {
    constructor() {
        this.subscriptions = this.loadSubscriptions();
        this.nodeLatencies = this.loadNodeLatencies();
        this.selectedNode = this.loadSelectedNode();
        this.loadBalancingEnabled = this.loadLoadBalancingEnabled();
        this.proxyEnabled = this.loadProxyEnabled();
        this.currentNodeIndex = 0; // 用于轮询负载均衡
        this.latencyTestInProgress = false;
        
        // 初始化默认订阅
        this.initializeDefaultSubscription();
        
        // 定期测试节点延迟
        this.startLatencyTesting();
    }

    /**
     * 加载订阅列表
     */
    loadSubscriptions() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.localStorageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('加载代理订阅失败:', error);
            return [];
        }
    }

    /**
     * 保存订阅列表
     */
    saveSubscriptions() {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.localStorageKey, JSON.stringify(this.subscriptions));
        } catch (error) {
            console.error('保存代理订阅失败:', error);
        }
    }

    /**
     * 加载节点延迟数据
     */
    loadNodeLatencies() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.nodeLatencyKey);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('加载节点延迟数据失败:', error);
            return {};
        }
    }

    /**
     * 保存节点延迟数据
     */
    saveNodeLatencies() {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.nodeLatencyKey, JSON.stringify(this.nodeLatencies));
        } catch (error) {
            console.error('保存节点延迟数据失败:', error);
        }
    }

    /**
     * 加载选中的节点
     */
    loadSelectedNode() {
        try {
            return localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.selectedNodeKey) || null;
        } catch (error) {
            console.error('加载选中节点失败:', error);
            return null;
        }
    }

    /**
     * 保存选中的节点
     */
    saveSelectedNode(nodeId) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.selectedNodeKey, nodeId);
            this.selectedNode = nodeId;
        } catch (error) {
            console.error('保存选中节点失败:', error);
        }
    }

    /**
     * 加载负载均衡启用状态
     */
    loadLoadBalancingEnabled() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.loadBalancingKey);
            return stored ? JSON.parse(stored) : true; // 默认启用
        } catch (error) {
            console.error('加载负载均衡状态失败:', error);
            return true;
        }
    }

    /**
     * 保存负载均衡启用状态
     */
    saveLoadBalancingEnabled(enabled) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.loadBalancingKey, JSON.stringify(enabled));
            this.loadBalancingEnabled = enabled;
        } catch (error) {
            console.error('保存负载均衡状态失败:', error);
        }
    }

    /**
     * 加载代理启用状态
     */
    loadProxyEnabled() {
        try {
            const stored = localStorage.getItem(PROXY_SUBSCRIPTION_CONFIG.proxyEnabledKey);
            return stored ? JSON.parse(stored) : true; // 默认启用
        } catch (error) {
            console.error('加载代理启用状态失败:', error);
            return true;
        }
    }

    /**
     * 保存代理启用状态
     */
    saveProxyEnabled(enabled) {
        try {
            localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.proxyEnabledKey, JSON.stringify(enabled));
            this.proxyEnabled = enabled;
        } catch (error) {
            console.error('保存代理启用状态失败:', error);
        }
    }

    /**
     * 初始化默认订阅
     */
    async initializeDefaultSubscription() {
        if (this.subscriptions.length === 0) {
            await this.addSubscription(
                '默认订阅',
                PROXY_SUBSCRIPTION_CONFIG.defaultSubscriptionUrl,
                true
            );
        }
    }

    /**
     * 添加订阅
     */
    async addSubscription(name, url, autoUpdate = true) {
        try {
            const subscription = {
                id: Date.now().toString(),
                name: name,
                url: url,
                autoUpdate: autoUpdate,
                nodes: [],
                lastUpdate: null,
                status: 'pending'
            };

            this.subscriptions.push(subscription);
            this.saveSubscriptions();

            // 立即更新订阅
            await this.updateSubscription(subscription.id);
            
            return subscription;
        } catch (error) {
            console.error('添加订阅失败:', error);
            throw error;
        }
    }

    /**
     * 删除订阅
     */
    removeSubscription(subscriptionId) {
        try {
            this.subscriptions = this.subscriptions.filter(sub => sub.id !== subscriptionId);
            this.saveSubscriptions();
            
            // 清理相关的延迟数据
            Object.keys(this.nodeLatencies).forEach(nodeId => {
                if (nodeId.startsWith(subscriptionId + '_')) {
                    delete this.nodeLatencies[nodeId];
                }
            });
            this.saveNodeLatencies();
            
            return true;
        } catch (error) {
            console.error('删除订阅失败:', error);
            return false;
        }
    }

    /**
     * 更新订阅
     */
    async updateSubscription(subscriptionId) {
        try {
            const subscription = this.subscriptions.find(sub => sub.id === subscriptionId);
            if (!subscription) {
                throw new Error('订阅不存在');
            }

            subscription.status = 'updating';
            
            // 获取订阅内容
            const nodes = await this.fetchSubscriptionNodes(subscription.url);
            
            subscription.nodes = nodes;
            subscription.lastUpdate = new Date().toISOString();
            subscription.status = 'active';
            
            this.saveSubscriptions();
            
            // 测试新节点的延迟
            await this.testNodesLatency(nodes);
            
            return subscription;
        } catch (error) {
            console.error('更新订阅失败:', error);
            const subscription = this.subscriptions.find(sub => sub.id === subscriptionId);
            if (subscription) {
                subscription.status = 'error';
                this.saveSubscriptions();
            }
            throw error;
        }
    }

    /**
     * 获取订阅节点
     */
    async fetchSubscriptionNodes(subscriptionUrl) {
        console.log('🔄 开始获取订阅节点:', subscriptionUrl);

        try {
            // 检查URL格式
            if (!subscriptionUrl) {
                throw new Error('订阅URL为空');
            }

            // 如果是data URL，直接解析
            if (subscriptionUrl.startsWith('data:')) {
                console.log('📄 检测到data URL，直接解析');
                const content = this.extractDataUrlContent(subscriptionUrl);
                return this.parseSubscriptionContent(content);
            }

            // 尝试多种获取方式
            let content = null;
            let lastError = null;

            // 方式1: 使用代理系统
            try {
                console.log('🌐 尝试使用代理系统获取订阅');
                content = await this.fetchWithProxy(subscriptionUrl);
                console.log('✅ 代理获取成功，内容长度:', content.length);
            } catch (error) {
                console.warn('⚠️ 代理获取失败:', error.message);
                lastError = error;
            }

            // 方式2: 直接获取（如果代理失败）
            if (!content) {
                try {
                    console.log('🔗 尝试直接获取订阅');
                    content = await this.fetchDirect(subscriptionUrl);
                    console.log('✅ 直接获取成功，内容长度:', content.length);
                } catch (error) {
                    console.warn('⚠️ 直接获取失败:', error.message);
                    lastError = error;
                }
            }

            // 方式3: 使用CORS代理（最后尝试）
            if (!content) {
                try {
                    console.log('🔄 尝试使用CORS代理获取订阅');
                    content = await this.fetchWithCorsProxy(subscriptionUrl);
                    console.log('✅ CORS代理获取成功，内容长度:', content.length);
                } catch (error) {
                    console.warn('⚠️ CORS代理获取失败:', error.message);
                    lastError = error;
                }
            }

            if (!content) {
                throw lastError || new Error('所有获取方式都失败了');
            }

            console.log('📝 开始解析订阅内容');
            const nodes = this.parseSubscriptionContent(content);
            console.log('✅ 订阅解析完成，节点数量:', nodes.length);

            return nodes;
        } catch (error) {
            console.error('❌ 获取订阅节点失败:', error);
            throw error;
        }
    }

    /**
     * 提取data URL内容
     */
    extractDataUrlContent(dataUrl) {
        try {
            const [header, data] = dataUrl.split(',');
            if (header.includes('base64')) {
                return atob(data);
            } else {
                return decodeURIComponent(data);
            }
        } catch (error) {
            console.error('解析data URL失败:', error);
            throw new Error('无效的data URL格式');
        }
    }

    /**
     * 使用代理系统获取
     */
    async fetchWithProxy(subscriptionUrl) {
        const proxiedUrl = await window.ProxyAuth?.addAuthToProxyUrl ?
            await window.ProxyAuth.addAuthToProxyUrl(PROXY_URL + encodeURIComponent(subscriptionUrl)) :
            PROXY_URL + encodeURIComponent(subscriptionUrl);

        console.log('🌐 代理URL:', proxiedUrl);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 增加超时时间

        const response = await fetch(proxiedUrl, {
            signal: controller.signal,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/plain,*/*'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.text();
    }

    /**
     * 直接获取
     */
    async fetchDirect(subscriptionUrl) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(subscriptionUrl, {
            signal: controller.signal,
            mode: 'cors',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/plain,*/*'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.text();
    }

    /**
     * 使用CORS代理获取
     */
    async fetchWithCorsProxy(subscriptionUrl) {
        const corsProxies = [
            'https://api.allorigins.win/raw?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://thingproxy.freeboard.io/fetch/'
        ];

        for (const proxy of corsProxies) {
            try {
                const proxyUrl = proxy + encodeURIComponent(subscriptionUrl);
                console.log('🔄 尝试CORS代理:', proxy);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);

                const response = await fetch(proxyUrl, {
                    signal: controller.signal,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    return await response.text();
                }
            } catch (error) {
                console.warn(`CORS代理 ${proxy} 失败:`, error.message);
                continue;
            }
        }

        throw new Error('所有CORS代理都失败了');
    }

    /**
     * 解析订阅内容
     */
    parseSubscriptionContent(content) {
        try {
            const nodes = [];
            console.log('📝 开始解析订阅内容，原始长度:', content.length);

            if (!content || content.length === 0) {
                console.warn('⚠️ 订阅内容为空');
                return [];
            }

            // 显示内容预览（前200字符）
            console.log('📄 内容预览:', content.substring(0, 200) + (content.length > 200 ? '...' : ''));

            // 尝试解析base64编码的内容
            let decodedContent = content;
            try {
                // 检查是否是base64编码（去除空白字符后检查）
                const cleanContent = content.replace(/\s/g, '');
                if (cleanContent.match(/^[A-Za-z0-9+/]+=*$/) && cleanContent.length % 4 === 0) {
                    decodedContent = atob(cleanContent);
                    console.log('✅ 检测到base64编码，已解码，解码后长度:', decodedContent.length);
                    console.log('📄 解码后预览:', decodedContent.substring(0, 200) + (decodedContent.length > 200 ? '...' : ''));
                }
            } catch (e) {
                // 不是base64编码，使用原始内容
                console.log('ℹ️ 非base64编码，使用原始内容');
            }

            // 分割行并清理
            const lines = decodedContent.split(/\r?\n/);
            console.log('📋 解析到行数:', lines.length);

            let validLines = 0;
            let skippedLines = 0;

            for (let i = 0; i < lines.length; i++) {
                const originalLine = lines[i];
                const trimmedLine = originalLine.trim();

                // 跳过空行和注释
                if (!trimmedLine || trimmedLine.startsWith('#') || trimmedLine.startsWith('//')) {
                    skippedLines++;
                    continue;
                }

                validLines++;
                console.log(`🔍 解析第${i+1}行 (有效第${validLines}行):`, trimmedLine);

                // 尝试解析不同格式的代理配置
                const node = this.parseProxyLine(trimmedLine);
                if (node) {
                    nodes.push(node);
                    console.log('✅ 成功解析节点:', node.name, '协议:', node.protocol);
                } else {
                    console.log('❌ 无法解析行:', trimmedLine);
                }
            }

            console.log('📊 解析统计:');
            console.log('  - 总行数:', lines.length);
            console.log('  - 有效行数:', validLines);
            console.log('  - 跳过行数:', skippedLines);
            console.log('  - 成功解析节点数:', nodes.length);
            console.log('  - 解析成功率:', validLines > 0 ? ((nodes.length / validLines) * 100).toFixed(1) + '%' : '0%');

            if (nodes.length === 0 && validLines > 0) {
                console.warn('⚠️ 警告: 有有效行但未解析出任何节点，可能是格式不支持');
                console.log('💡 支持的格式示例:');
                console.log('  - http://proxy.example.com:8080');
                console.log('  - https://user:<EMAIL>:8443');
                console.log('  - socks5://proxy.example.com:1080');
                console.log('  - vmess://base64编码');
                console.log('  - ss://base64编码');
                console.log('  - trojan://password@host:port');
                console.log('  - proxy.example.com:3128');
            }

            return nodes;
        } catch (error) {
            console.error('❌ 解析订阅内容失败:', error);
            console.error('📄 失败时的内容:', content.substring(0, 500));
            return [];
        }
    }

    /**
     * 解析代理行
     */
    parseProxyLine(line) {
        try {
            console.log('尝试解析代理行:', line);

            // 支持多种格式的代理配置
            // 格式1: http://proxy.example.com:8080
            // 格式2: https://user:<EMAIL>:8080
            // 格式3: socks5://proxy.example.com:1080
            // 格式4: vmess://base64编码
            // 格式5: ss://base64编码
            // 格式6: trojan://密码@host:port

            // 处理vmess协议
            if (line.startsWith('vmess://')) {
                try {
                    const base64Part = line.substring(8);
                    const decoded = JSON.parse(atob(base64Part));
                    return {
                        id: `vmess_${decoded.add}_${decoded.port}`,
                        name: `VMess ${decoded.ps || decoded.add}:${decoded.port}`,
                        protocol: 'vmess',
                        host: decoded.add,
                        port: parseInt(decoded.port),
                        auth: decoded.id,
                        url: line,
                        latency: null,
                        status: 'unknown'
                    };
                } catch (e) {
                    console.error('解析vmess失败:', e);
                }
            }

            // 处理ss协议
            if (line.startsWith('ss://')) {
                try {
                    const base64Part = line.substring(5);
                    const decoded = atob(base64Part);
                    const [method, rest] = decoded.split(':');
                    const [password, hostPort] = rest.split('@');
                    const [host, port] = hostPort.split(':');

                    return {
                        id: `ss_${host}_${port}`,
                        name: `SS ${host}:${port}`,
                        protocol: 'ss',
                        host: host,
                        port: parseInt(port),
                        auth: `${method}:${password}`,
                        url: line,
                        latency: null,
                        status: 'unknown'
                    };
                } catch (e) {
                    console.error('解析ss失败:', e);
                }
            }

            // 处理trojan协议
            if (line.startsWith('trojan://')) {
                try {
                    const url = new URL(line);
                    return {
                        id: `trojan_${url.hostname}_${url.port}`,
                        name: `Trojan ${url.hostname}:${url.port}`,
                        protocol: 'trojan',
                        host: url.hostname,
                        port: parseInt(url.port) || 443,
                        auth: url.username,
                        url: line,
                        latency: null,
                        status: 'unknown'
                    };
                } catch (e) {
                    console.error('解析trojan失败:', e);
                }
            }

            // 处理标准HTTP/HTTPS/SOCKS5协议
            const urlMatch = line.match(/^(https?|socks5):\/\/(.+)/);
            if (urlMatch) {
                const protocol = urlMatch[1];
                const hostPart = urlMatch[2];

                // 解析主机和端口
                let host, port, auth;

                if (hostPart.includes('@')) {
                    const [authPart, hostPortPart] = hostPart.split('@');
                    auth = authPart;
                    [host, port] = hostPortPart.split(':');
                } else {
                    [host, port] = hostPart.split(':');
                }

                const finalPort = parseInt(port) || (protocol === 'https' ? 443 : 80);

                const node = {
                    id: `${protocol}_${host}_${finalPort}`,
                    name: `${protocol.toUpperCase()} ${host}:${finalPort}`,
                    protocol: protocol,
                    host: host,
                    port: finalPort,
                    auth: auth,
                    url: line,
                    latency: null,
                    status: 'unknown'
                };

                console.log('成功解析标准协议节点:', node);
                return node;
            }

            // 尝试解析简单的host:port格式
            const simpleMatch = line.match(/^([^:]+):(\d+)$/);
            if (simpleMatch) {
                const host = simpleMatch[1];
                const port = parseInt(simpleMatch[2]);

                const node = {
                    id: `http_${host}_${port}`,
                    name: `HTTP ${host}:${port}`,
                    protocol: 'http',
                    host: host,
                    port: port,
                    auth: null,
                    url: `http://${line}`,
                    latency: null,
                    status: 'unknown'
                };

                console.log('成功解析简单格式节点:', node);
                return node;
            }

            console.log('无法解析的格式:', line);
            return null;
        } catch (error) {
            console.error('解析代理行失败:', line, error);
            return null;
        }
    }

    /**
     * 测试节点延迟
     */
    async testNodesLatency(nodes) {
        if (this.latencyTestInProgress) {
            return;
        }

        this.latencyTestInProgress = true;

        try {
            const testPromises = nodes.map(node => this.testNodeLatency(node));
            await Promise.allSettled(testPromises);
            this.saveNodeLatencies();
        } catch (error) {
            console.error('测试节点延迟失败:', error);
        } finally {
            this.latencyTestInProgress = false;
        }
    }

    /**
     * 测试单个节点延迟
     */
    async testNodeLatency(node) {
        try {
            const startTime = Date.now();

            // 使用代理节点测试一个简单的请求
            const testUrl = 'https://httpbin.org/get';
            const proxiedUrl = this.buildProxyUrl(node, testUrl);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), PROXY_SUBSCRIPTION_CONFIG.latencyTestTimeout);

            const response = await fetch(proxiedUrl, {
                method: 'GET',
                signal: controller.signal,
                cache: 'no-cache'
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const latency = Date.now() - startTime;
                this.nodeLatencies[node.id] = {
                    latency: latency,
                    lastTest: Date.now(),
                    status: 'online'
                };
                node.latency = latency;
                node.status = 'online';
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            this.nodeLatencies[node.id] = {
                latency: null,
                lastTest: Date.now(),
                status: 'offline',
                error: error.message
            };
            node.latency = null;
            node.status = 'offline';
        }
    }

    /**
     * 构建代理URL
     */
    buildProxyUrl(node, targetUrl) {
        try {
            // 这里需要根据实际的代理实现来构建URL
            // 目前使用现有的代理系统，但记录使用的节点
            console.log('使用代理节点:', node.name, '延迟:', node.latency + 'ms');

            // 在实际部署中，这里应该根据node的配置来构建真正的代理URL
            // 例如: return `${node.url}/proxy/${encodeURIComponent(targetUrl)}`;

            // 目前使用现有的代理系统
            return PROXY_URL + encodeURIComponent(targetUrl);
        } catch (error) {
            console.error('构建代理URL失败:', error);
            return PROXY_URL + encodeURIComponent(targetUrl);
        }
    }

    /**
     * 获取最佳节点
     */
    getBestNode() {
        if (!this.proxyEnabled) {
            return null;
        }

        const allNodes = this.getAllNodes();
        const onlineNodes = allNodes.filter(node =>
            this.nodeLatencies[node.id] &&
            this.nodeLatencies[node.id].status === 'online'
        );

        if (onlineNodes.length === 0) {
            return null;
        }

        if (!this.loadBalancingEnabled) {
            // 如果没有启用负载均衡，返回延迟最低的节点
            return onlineNodes.reduce((best, current) => {
                const bestLatency = this.nodeLatencies[best.id]?.latency || Infinity;
                const currentLatency = this.nodeLatencies[current.id]?.latency || Infinity;
                return currentLatency < bestLatency ? current : best;
            });
        }

        // 负载均衡策略
        const strategy = PROXY_SUBSCRIPTION_CONFIG.defaultStrategy;

        switch (strategy) {
            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.ROUND_ROBIN:
                return this.getRoundRobinNode(onlineNodes);

            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.RANDOM:
                return onlineNodes[Math.floor(Math.random() * onlineNodes.length)];

            case PROXY_SUBSCRIPTION_CONFIG.loadBalancingStrategies.LEAST_LATENCY:
            default:
                // 选择延迟最低的几个节点中的一个
                const sortedNodes = onlineNodes.sort((a, b) => {
                    const aLatency = this.nodeLatencies[a.id]?.latency || Infinity;
                    const bLatency = this.nodeLatencies[b.id]?.latency || Infinity;
                    return aLatency - bLatency;
                });

                // 从前25%的节点中随机选择一个
                const topCount = Math.max(1, Math.ceil(sortedNodes.length * 0.25));
                const topNodes = sortedNodes.slice(0, topCount);
                return topNodes[Math.floor(Math.random() * topNodes.length)];
        }
    }

    /**
     * 轮询获取节点
     */
    getRoundRobinNode(nodes) {
        if (nodes.length === 0) return null;

        const node = nodes[this.currentNodeIndex % nodes.length];
        this.currentNodeIndex = (this.currentNodeIndex + 1) % nodes.length;
        return node;
    }

    /**
     * 获取所有节点
     */
    getAllNodes() {
        const allNodes = [];
        for (const subscription of this.subscriptions) {
            if (subscription.status === 'active' && subscription.nodes) {
                allNodes.push(...subscription.nodes);
            }
        }
        return allNodes;
    }

    /**
     * 开始延迟测试
     */
    startLatencyTesting() {
        // 立即测试一次
        setTimeout(() => {
            this.testAllNodesLatency();
        }, 1000);

        // 定期测试
        setInterval(() => {
            this.testAllNodesLatency();
        }, PROXY_SUBSCRIPTION_CONFIG.latencyTestInterval);
    }

    /**
     * 测试所有节点延迟
     */
    async testAllNodesLatency() {
        const allNodes = this.getAllNodes();
        if (allNodes.length > 0) {
            await this.testNodesLatency(allNodes);
        }
    }

    /**
     * 获取代理统计信息
     */
    getStats() {
        const allNodes = this.getAllNodes();
        const onlineNodes = allNodes.filter(node =>
            this.nodeLatencies[node.id] &&
            this.nodeLatencies[node.id].status === 'online'
        );

        return {
            totalSubscriptions: this.subscriptions.length,
            totalNodes: allNodes.length,
            onlineNodes: onlineNodes.length,
            offlineNodes: allNodes.length - onlineNodes.length,
            proxyEnabled: this.proxyEnabled,
            loadBalancingEnabled: this.loadBalancingEnabled,
            selectedNode: this.selectedNode,
            bestNode: this.getBestNode()
        };
    }
}

// 创建全局实例
window.ProxySubscriptionManager = new ProxySubscriptionManager();
