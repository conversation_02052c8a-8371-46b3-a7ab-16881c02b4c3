# LibreTV 代理功能使用说明

## 🚀 快速开始

### 1. 基本使用
代理功能默认启用，无需额外配置即可使用。

### 2. 访问设置
- 打开 LibreTV 主页
- 点击右上角的设置图标 ⚙️
- 找到"代理订阅管理"部分

## 🔧 功能说明

### 代理状态面板
显示当前代理系统的状态信息：
- **代理状态**: 启用/禁用
- **在线节点**: 当前可用节点数量
- **当前节点**: 正在使用的代理节点
- **负载均衡**: 是否启用负载均衡

### 订阅管理
- **添加订阅**: 点击"+"按钮添加新的代理订阅
- **默认订阅**: 系统预设了默认订阅 `https://sub.0407123.xyz/admin?sub`
- **更新订阅**: 点击"↻"按钮更新订阅内容
- **删除订阅**: 点击"×"按钮删除订阅

### 播放器控制
在视频播放器中，控制栏右侧有代理开关：
- 🌐 = 代理已启用
- 🚫 = 代理已禁用
- 点击可快速切换代理状态

## 🛠️ 问题解决

### 问题1: 更新订阅无节点
**症状**: 添加订阅后显示0个节点

**解决方案**:
1. 检查订阅URL是否可访问
2. 在浏览器控制台运行: `runProxyFixes()`
3. 查看控制台日志了解解析详情

**支持的订阅格式**:
```
# HTTP/HTTPS代理
http://proxy.example.com:8080
https://user:<EMAIL>:8443

# SOCKS5代理
socks5://proxy.example.com:1080

# 简单格式
proxy.example.com:3128

# VMess (base64编码)
vmess://eyJ2IjoiMiIsInBzIjoi...

# Shadowsocks
ss://YWVzLTI1Ni1nY206...

# Trojan
trojan://password@host:port
```

### 问题2: ArtPlayer代理开关无反应
**症状**: 点击播放器中的代理开关没有反应

**解决方案**:
1. 确保在播放器页面 (player.html)
2. 等待播放器完全加载
3. 在控制台运行: `fixProxyIssues()`
4. 检查控制台是否有错误信息

### 问题3: 负载均衡开关无滑动效果
**症状**: 负载均衡开关点击后没有滑动动画

**解决方案**:
1. 刷新页面
2. 在控制台运行: `fixProxyIssues()`
3. 检查CSS样式是否正确加载

## 🧪 测试和调试

### 控制台命令
在浏览器控制台中可以使用以下命令：

```javascript
// 查看代理状态
debugProxy()

// 运行完整测试
runProxyTests()

// 运行问题修复
runProxyFixes()

// 快速修复常见问题
fixProxyIssues()

// 手动测试节点
testAllNodes()

// 刷新订阅
refreshSubscriptions()
```

### 演示页面
访问 `proxy-demo.html` 可以进行交互式测试：
- 实时状态监控
- 订阅管理操作
- 功能测试按钮
- 操作日志显示

## 📊 高级功能

### 负载均衡策略
系统支持多种负载均衡策略：
- **最低延迟** (默认): 从延迟最低的25%节点中随机选择
- **轮询**: 依次使用各个节点
- **随机**: 随机选择可用节点

### 自动节点测试
- 每5分钟自动测试所有节点延迟
- 自动剔除离线节点
- 智能选择最优节点

### 多用户负载分布
- 针对Cloudflare部署优化
- 自动分配不同用户到不同节点
- 防止单个节点过载

## 🔍 故障排除

### 常见问题检查清单
1. ✅ 确保所有脚本文件已加载
2. ✅ 检查浏览器控制台是否有错误
3. ✅ 验证订阅URL是否可访问
4. ✅ 确认代理节点是否在线
5. ✅ 检查网络连接是否正常

### 日志分析
查看浏览器控制台日志：
- `[INFO]` - 正常信息
- `[SUCCESS]` - 操作成功
- `[WARNING]` - 警告信息
- `[ERROR]` - 错误信息

### 重置配置
如果遇到严重问题，可以重置配置：
```javascript
// 清除所有代理配置
localStorage.removeItem('proxySubscriptions');
localStorage.removeItem('proxyNodeLatencies');
localStorage.removeItem('selectedProxyNode');
localStorage.removeItem('proxyEnabled');
localStorage.removeItem('proxyLoadBalancing');

// 刷新页面
location.reload();
```

## 📞 技术支持

如果问题仍然存在：
1. 运行 `runProxyFixes()` 获取详细诊断信息
2. 复制控制台输出的错误信息
3. 提供具体的操作步骤和期望结果
4. 说明使用的浏览器和版本

## 🎯 最佳实践

1. **定期更新订阅**: 保持代理节点列表最新
2. **监控节点状态**: 关注在线节点数量变化
3. **合理使用负载均衡**: 根据实际需求开启/关闭
4. **及时处理错误**: 查看控制台日志并及时处理问题
5. **备份配置**: 定期导出配置以防数据丢失
