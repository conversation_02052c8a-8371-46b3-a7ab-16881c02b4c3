/**
 * 代理功能测试套件
 * 用于测试代理订阅管理、节点选择、负载均衡等功能
 */

class ProxyTestSuite {
    constructor() {
        this.testResults = [];
        this.testStartTime = null;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('开始运行代理功能测试套件...');
        this.testStartTime = Date.now();
        this.testResults = [];

        const tests = [
            { name: '代理管理器初始化测试', fn: this.testProxyManagerInitialization },
            { name: '订阅管理测试', fn: this.testSubscriptionManagement },
            { name: '节点解析测试', fn: this.testNodeParsing },
            { name: '延迟测试功能', fn: this.testLatencyTesting },
            { name: '节点选择算法测试', fn: this.testNodeSelection },
            { name: '负载均衡测试', fn: this.testLoadBalancing },
            { name: '代理开关测试', fn: this.testProxyToggle },
            { name: 'UI集成测试', fn: this.testUIIntegration },
            { name: '存储持久化测试', fn: this.testStoragePersistence },
            { name: '错误处理测试', fn: this.testErrorHandling }
        ];

        for (const test of tests) {
            try {
                console.log(`运行测试: ${test.name}`);
                const result = await test.fn.call(this);
                this.testResults.push({
                    name: test.name,
                    status: 'passed',
                    result: result,
                    duration: Date.now() - this.testStartTime
                });
                console.log(`✅ ${test.name} - 通过`);
            } catch (error) {
                this.testResults.push({
                    name: test.name,
                    status: 'failed',
                    error: error.message,
                    duration: Date.now() - this.testStartTime
                });
                console.error(`❌ ${test.name} - 失败:`, error.message);
            }
        }

        this.generateTestReport();
        return this.testResults;
    }

    /**
     * 测试代理管理器初始化
     */
    async testProxyManagerInitialization() {
        if (!window.ProxySubscriptionManager) {
            throw new Error('ProxySubscriptionManager 未初始化');
        }

        const manager = window.ProxySubscriptionManager;
        
        // 检查基本属性
        if (typeof manager.subscriptions === 'undefined') {
            throw new Error('subscriptions 属性未定义');
        }
        
        if (typeof manager.nodeLatencies === 'undefined') {
            throw new Error('nodeLatencies 属性未定义');
        }
        
        if (typeof manager.proxyEnabled === 'undefined') {
            throw new Error('proxyEnabled 属性未定义');
        }

        // 检查基本方法
        const requiredMethods = [
            'addSubscription', 'removeSubscription', 'updateSubscription',
            'getBestNode', 'testNodeLatency', 'getStats'
        ];

        for (const method of requiredMethods) {
            if (typeof manager[method] !== 'function') {
                throw new Error(`方法 ${method} 不存在或不是函数`);
            }
        }

        return {
            subscriptionCount: manager.subscriptions.length,
            proxyEnabled: manager.proxyEnabled,
            loadBalancingEnabled: manager.loadBalancingEnabled
        };
    }

    /**
     * 测试订阅管理
     */
    async testSubscriptionManagement() {
        const manager = window.ProxySubscriptionManager;
        const initialCount = manager.subscriptions.length;

        // 测试添加订阅
        const testSubscription = await manager.addSubscription(
            '测试订阅',
            'https://example.com/test-subscription',
            false
        );

        if (!testSubscription || !testSubscription.id) {
            throw new Error('添加订阅失败');
        }

        if (manager.subscriptions.length !== initialCount + 1) {
            throw new Error('订阅数量不正确');
        }

        // 测试删除订阅
        const removed = manager.removeSubscription(testSubscription.id);
        if (!removed) {
            throw new Error('删除订阅失败');
        }

        if (manager.subscriptions.length !== initialCount) {
            throw new Error('删除后订阅数量不正确');
        }

        return {
            addSubscriptionSuccess: true,
            removeSubscriptionSuccess: true,
            finalCount: manager.subscriptions.length
        };
    }

    /**
     * 测试节点解析
     */
    async testNodeParsing() {
        const manager = window.ProxySubscriptionManager;
        
        // 测试不同格式的代理配置
        const testLines = [
            'http://proxy1.example.com:8080',
            'https://user:<EMAIL>:8443',
            'socks5://proxy3.example.com:1080',
            '# 这是注释',
            '',
            'invalid-line'
        ];

        const results = [];
        for (const line of testLines) {
            const node = manager.parseProxyLine(line);
            results.push({
                line: line,
                parsed: !!node,
                node: node
            });
        }

        // 验证解析结果
        const validNodes = results.filter(r => r.parsed);
        if (validNodes.length !== 3) {
            throw new Error(`期望解析3个有效节点，实际解析${validNodes.length}个`);
        }

        return {
            totalLines: testLines.length,
            validNodes: validNodes.length,
            results: results
        };
    }

    /**
     * 测试延迟测试功能
     */
    async testLatencyTesting() {
        const manager = window.ProxySubscriptionManager;
        
        // 创建测试节点
        const testNode = {
            id: 'test_node_1',
            name: 'Test Node 1',
            protocol: 'http',
            host: 'httpbin.org',
            port: 80,
            url: 'http://httpbin.org',
            latency: null,
            status: 'unknown'
        };

        // 测试节点延迟
        await manager.testNodeLatency(testNode);

        // 检查延迟数据是否更新
        const latencyData = manager.nodeLatencies[testNode.id];
        if (!latencyData) {
            throw new Error('延迟数据未保存');
        }

        if (!latencyData.lastTest) {
            throw new Error('测试时间未记录');
        }

        return {
            nodeId: testNode.id,
            latency: latencyData.latency,
            status: latencyData.status,
            lastTest: latencyData.lastTest
        };
    }

    /**
     * 测试节点选择算法
     */
    async testNodeSelection() {
        const manager = window.ProxySubscriptionManager;
        
        // 创建模拟节点数据
        const mockNodes = [
            { id: 'node1', latency: 100, status: 'online' },
            { id: 'node2', latency: 50, status: 'online' },
            { id: 'node3', latency: 200, status: 'offline' },
            { id: 'node4', latency: 75, status: 'online' }
        ];

        // 模拟延迟数据
        const originalLatencies = manager.nodeLatencies;
        manager.nodeLatencies = {};
        mockNodes.forEach(node => {
            manager.nodeLatencies[node.id] = {
                latency: node.latency,
                status: node.status,
                lastTest: Date.now()
            };
        });

        // 模拟订阅数据
        const originalSubscriptions = manager.subscriptions;
        manager.subscriptions = [{
            id: 'test_sub',
            status: 'active',
            nodes: mockNodes
        }];

        // 测试最佳节点选择
        const bestNode = manager.getBestNode();
        
        // 恢复原始数据
        manager.nodeLatencies = originalLatencies;
        manager.subscriptions = originalSubscriptions;

        if (!bestNode) {
            throw new Error('未能选择最佳节点');
        }

        // 应该选择延迟最低的在线节点 (node2, 50ms)
        if (bestNode.id !== 'node2') {
            throw new Error(`期望选择node2，实际选择${bestNode.id}`);
        }

        return {
            selectedNode: bestNode.id,
            expectedNode: 'node2',
            onlineNodes: mockNodes.filter(n => n.status === 'online').length
        };
    }

    /**
     * 测试负载均衡
     */
    async testLoadBalancing() {
        const manager = window.ProxySubscriptionManager;
        
        // 启用负载均衡
        const originalLoadBalancing = manager.loadBalancingEnabled;
        manager.saveLoadBalancingEnabled(true);

        // 创建多个相同延迟的节点
        const mockNodes = [
            { id: 'lb_node1', latency: 50, status: 'online' },
            { id: 'lb_node2', latency: 50, status: 'online' },
            { id: 'lb_node3', latency: 50, status: 'online' }
        ];

        // 模拟数据
        const originalLatencies = manager.nodeLatencies;
        const originalSubscriptions = manager.subscriptions;
        
        manager.nodeLatencies = {};
        mockNodes.forEach(node => {
            manager.nodeLatencies[node.id] = {
                latency: node.latency,
                status: node.status,
                lastTest: Date.now()
            };
        });

        manager.subscriptions = [{
            id: 'lb_test_sub',
            status: 'active',
            nodes: mockNodes
        }];

        // 多次获取最佳节点，应该有分布
        const selections = [];
        for (let i = 0; i < 10; i++) {
            const node = manager.getBestNode();
            if (node) {
                selections.push(node.id);
            }
        }

        // 恢复原始数据
        manager.nodeLatencies = originalLatencies;
        manager.subscriptions = originalSubscriptions;
        manager.saveLoadBalancingEnabled(originalLoadBalancing);

        // 检查是否有分布（不是所有选择都相同）
        const uniqueSelections = [...new Set(selections)];
        
        return {
            totalSelections: selections.length,
            uniqueSelections: uniqueSelections.length,
            selections: selections,
            loadBalancingWorking: uniqueSelections.length > 1
        };
    }

    /**
     * 测试代理开关
     */
    async testProxyToggle() {
        const manager = window.ProxySubscriptionManager;
        
        const originalState = manager.proxyEnabled;
        
        // 测试禁用代理
        manager.saveProxyEnabled(false);
        if (manager.proxyEnabled !== false) {
            throw new Error('禁用代理失败');
        }

        // 测试启用代理
        manager.saveProxyEnabled(true);
        if (manager.proxyEnabled !== true) {
            throw new Error('启用代理失败');
        }

        // 恢复原始状态
        manager.saveProxyEnabled(originalState);

        return {
            toggleTest: 'passed',
            finalState: manager.proxyEnabled
        };
    }

    /**
     * 测试UI集成
     */
    async testUIIntegration() {
        // 检查UI元素是否存在
        const requiredElements = [
            'proxyStatus',
            'onlineNodes', 
            'currentNode',
            'subscriptionsList',
            'loadBalancingToggle'
        ];

        const missingElements = [];
        for (const elementId of requiredElements) {
            if (!document.getElementById(elementId)) {
                missingElements.push(elementId);
            }
        }

        if (missingElements.length > 0) {
            throw new Error(`缺少UI元素: ${missingElements.join(', ')}`);
        }

        // 检查UI函数是否存在
        const requiredFunctions = [
            'showAddSubscriptionForm',
            'addSubscription',
            'removeSubscription',
            'testAllNodes',
            'refreshSubscriptions'
        ];

        const missingFunctions = [];
        for (const funcName of requiredFunctions) {
            if (typeof window[funcName] !== 'function') {
                missingFunctions.push(funcName);
            }
        }

        if (missingFunctions.length > 0) {
            throw new Error(`缺少UI函数: ${missingFunctions.join(', ')}`);
        }

        return {
            uiElementsFound: requiredElements.length - missingElements.length,
            uiFunctionsFound: requiredFunctions.length - missingFunctions.length,
            allElementsPresent: missingElements.length === 0,
            allFunctionsPresent: missingFunctions.length === 0
        };
    }

    /**
     * 测试存储持久化
     */
    async testStoragePersistence() {
        const manager = window.ProxySubscriptionManager;
        
        // 测试订阅数据持久化
        const testData = {
            subscriptions: [{ id: 'test', name: 'Test Sub' }],
            nodeLatencies: { 'test_node': { latency: 100, status: 'online' } },
            proxyEnabled: false,
            loadBalancingEnabled: true
        };

        // 保存测试数据
        localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.localStorageKey, JSON.stringify(testData.subscriptions));
        localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.nodeLatencyKey, JSON.stringify(testData.nodeLatencies));
        localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.proxyEnabledKey, JSON.stringify(testData.proxyEnabled));
        localStorage.setItem(PROXY_SUBSCRIPTION_CONFIG.loadBalancingKey, JSON.stringify(testData.loadBalancingEnabled));

        // 重新加载数据
        const loadedSubscriptions = manager.loadSubscriptions();
        const loadedLatencies = manager.loadNodeLatencies();
        const loadedProxyEnabled = manager.loadProxyEnabled();
        const loadedLoadBalancing = manager.loadLoadBalancingEnabled();

        // 验证数据
        if (JSON.stringify(loadedSubscriptions) !== JSON.stringify(testData.subscriptions)) {
            throw new Error('订阅数据持久化失败');
        }

        if (JSON.stringify(loadedLatencies) !== JSON.stringify(testData.nodeLatencies)) {
            throw new Error('延迟数据持久化失败');
        }

        if (loadedProxyEnabled !== testData.proxyEnabled) {
            throw new Error('代理状态持久化失败');
        }

        if (loadedLoadBalancing !== testData.loadBalancingEnabled) {
            throw new Error('负载均衡状态持久化失败');
        }

        return {
            subscriptionsPersisted: true,
            latenciesPersisted: true,
            proxyStatePersisted: true,
            loadBalancingPersisted: true
        };
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        const manager = window.ProxySubscriptionManager;
        
        const results = {
            invalidSubscriptionUrl: false,
            nonExistentSubscriptionRemoval: false,
            invalidNodeParsing: false
        };

        // 测试无效订阅URL
        try {
            await manager.addSubscription('Invalid', 'not-a-url', false);
        } catch (error) {
            results.invalidSubscriptionUrl = true;
        }

        // 测试删除不存在的订阅
        const removeResult = manager.removeSubscription('non-existent-id');
        results.nonExistentSubscriptionRemoval = !removeResult;

        // 测试无效节点解析
        const invalidNode = manager.parseProxyLine('invalid-proxy-config');
        results.invalidNodeParsing = !invalidNode;

        return results;
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.status === 'passed').length;
        const failedTests = totalTests - passedTests;
        const totalDuration = Date.now() - this.testStartTime;

        const report = {
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                duration: totalDuration,
                successRate: ((passedTests / totalTests) * 100).toFixed(2) + '%'
            },
            details: this.testResults
        };

        console.log('\n=== 代理功能测试报告 ===');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`成功率: ${report.summary.successRate}`);
        console.log(`总耗时: ${totalDuration}ms`);
        console.log('\n详细结果:');
        
        this.testResults.forEach(result => {
            const status = result.status === 'passed' ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            if (result.status === 'failed') {
                console.log(`   错误: ${result.error}`);
            }
        });

        return report;
    }
}

// 创建全局测试实例
window.ProxyTestSuite = new ProxyTestSuite();

// 添加快捷测试函数
window.runProxyTests = function() {
    return window.ProxyTestSuite.runAllTests();
};
