/**
 * 订阅调试和测试工具
 * 专门用于诊断和修复订阅更新问题
 */

class SubscriptionDebugger {
    constructor() {
        this.testResults = [];
    }

    /**
     * 测试订阅URL
     */
    async testSubscriptionUrl(url, name = '测试订阅') {
        console.log('🧪 开始测试订阅URL:', url);
        
        const result = {
            url: url,
            name: name,
            timestamp: new Date().toISOString(),
            steps: [],
            success: false,
            nodes: [],
            error: null
        };

        try {
            // 步骤1: 验证URL格式
            result.steps.push('✅ URL格式验证通过');
            
            if (!url) {
                throw new Error('URL为空');
            }

            try {
                new URL(url);
                result.steps.push('✅ URL格式有效');
            } catch (e) {
                if (!url.startsWith('data:')) {
                    throw new Error('URL格式无效: ' + e.message);
                }
                result.steps.push('✅ Data URL格式');
            }

            // 步骤2: 尝试获取内容
            result.steps.push('🔄 开始获取订阅内容...');
            
            let content = null;
            const manager = window.ProxySubscriptionManager;
            
            if (!manager) {
                throw new Error('ProxySubscriptionManager未初始化');
            }

            // 使用增强的获取方法
            try {
                const nodes = await manager.fetchSubscriptionNodes(url);
                result.nodes = nodes;
                result.steps.push(`✅ 成功获取并解析订阅，节点数: ${nodes.length}`);
                
                if (nodes.length === 0) {
                    result.steps.push('⚠️ 警告: 未解析出任何节点');
                } else {
                    result.steps.push('📋 节点列表:');
                    nodes.forEach((node, index) => {
                        result.steps.push(`  ${index + 1}. ${node.name} (${node.protocol})`);
                    });
                }
                
                result.success = true;
            } catch (error) {
                result.error = error.message;
                result.steps.push('❌ 获取订阅失败: ' + error.message);
                
                // 尝试手动测试
                result.steps.push('🔄 尝试手动获取内容进行诊断...');
                await this.manualContentTest(url, result);
            }

        } catch (error) {
            result.error = error.message;
            result.steps.push('❌ 测试失败: ' + error.message);
        }

        this.testResults.push(result);
        this.printTestResult(result);
        return result;
    }

    /**
     * 手动内容测试
     */
    async manualContentTest(url, result) {
        const testMethods = [
            { name: '直接获取', method: this.testDirectFetch },
            { name: '代理获取', method: this.testProxyFetch },
            { name: 'CORS代理', method: this.testCorsProxyFetch }
        ];

        for (const testMethod of testMethods) {
            try {
                result.steps.push(`🔄 尝试${testMethod.name}...`);
                const content = await testMethod.method.call(this, url);
                
                if (content) {
                    result.steps.push(`✅ ${testMethod.name}成功，内容长度: ${content.length}`);
                    result.steps.push(`📄 内容预览: ${content.substring(0, 100)}...`);
                    
                    // 尝试解析内容
                    const manager = window.ProxySubscriptionManager;
                    if (manager) {
                        const nodes = manager.parseSubscriptionContent(content);
                        result.steps.push(`📝 解析结果: ${nodes.length} 个节点`);
                        if (nodes.length > 0) {
                            result.nodes = nodes;
                            result.success = true;
                            break;
                        }
                    }
                } else {
                    result.steps.push(`❌ ${testMethod.name}返回空内容`);
                }
            } catch (error) {
                result.steps.push(`❌ ${testMethod.name}失败: ${error.message}`);
            }
        }
    }

    /**
     * 直接获取测试
     */
    async testDirectFetch(url) {
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.text();
    }

    /**
     * 代理获取测试
     */
    async testProxyFetch(url) {
        const proxiedUrl = PROXY_URL + encodeURIComponent(url);
        const response = await fetch(proxiedUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.text();
    }

    /**
     * CORS代理获取测试
     */
    async testCorsProxyFetch(url) {
        const corsProxy = 'https://api.allorigins.win/raw?url=';
        const proxyUrl = corsProxy + encodeURIComponent(url);
        
        const response = await fetch(proxyUrl);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.text();
    }

    /**
     * 打印测试结果
     */
    printTestResult(result) {
        console.log('\n=== 订阅测试结果 ===');
        console.log('URL:', result.url);
        console.log('名称:', result.name);
        console.log('时间:', result.timestamp);
        console.log('成功:', result.success ? '✅' : '❌');
        console.log('节点数:', result.nodes.length);
        
        if (result.error) {
            console.log('错误:', result.error);
        }
        
        console.log('\n详细步骤:');
        result.steps.forEach(step => console.log(step));
        
        if (result.nodes.length > 0) {
            console.log('\n节点详情:');
            result.nodes.forEach((node, index) => {
                console.log(`${index + 1}. ${node.name}`);
                console.log(`   协议: ${node.protocol}`);
                console.log(`   主机: ${node.host}:${node.port}`);
                console.log(`   ID: ${node.id}`);
            });
        }
        
        console.log('\n========================\n');
    }

    /**
     * 测试默认订阅
     */
    async testDefaultSubscription() {
        const defaultUrl = PROXY_SUBSCRIPTION_CONFIG.defaultSubscriptionUrl;
        return await this.testSubscriptionUrl(defaultUrl, '默认订阅');
    }

    /**
     * 测试所有现有订阅
     */
    async testAllSubscriptions() {
        if (!window.ProxySubscriptionManager) {
            console.error('ProxySubscriptionManager未初始化');
            return;
        }

        const subscriptions = window.ProxySubscriptionManager.subscriptions;
        console.log(`🧪 开始测试所有订阅，共 ${subscriptions.length} 个`);

        const results = [];
        for (const sub of subscriptions) {
            const result = await this.testSubscriptionUrl(sub.url, sub.name);
            results.push(result);
        }

        // 生成汇总报告
        this.generateSummaryReport(results);
        return results;
    }

    /**
     * 生成汇总报告
     */
    generateSummaryReport(results) {
        const total = results.length;
        const successful = results.filter(r => r.success).length;
        const failed = total - successful;
        const totalNodes = results.reduce((sum, r) => sum + r.nodes.length, 0);

        console.log('\n=== 订阅测试汇总报告 ===');
        console.log(`总订阅数: ${total}`);
        console.log(`成功: ${successful} (${((successful/total)*100).toFixed(1)}%)`);
        console.log(`失败: ${failed} (${((failed/total)*100).toFixed(1)}%)`);
        console.log(`总节点数: ${totalNodes}`);

        if (failed > 0) {
            console.log('\n失败的订阅:');
            results.filter(r => !r.success).forEach((result, index) => {
                console.log(`${index + 1}. ${result.name}: ${result.error}`);
            });
        }

        if (successful > 0) {
            console.log('\n成功的订阅:');
            results.filter(r => r.success).forEach((result, index) => {
                console.log(`${index + 1}. ${result.name}: ${result.nodes.length} 个节点`);
            });
        }

        console.log('\n=========================\n');
    }

    /**
     * 修复订阅问题
     */
    async fixSubscriptionIssues() {
        console.log('🔧 开始修复订阅问题...');

        if (!window.ProxySubscriptionManager) {
            console.error('❌ ProxySubscriptionManager未初始化');
            return;
        }

        const manager = window.ProxySubscriptionManager;
        const subscriptions = manager.subscriptions;

        console.log(`📋 检查 ${subscriptions.length} 个订阅`);

        for (const subscription of subscriptions) {
            console.log(`🔍 检查订阅: ${subscription.name}`);

            // 检查订阅状态
            if (subscription.status === 'error') {
                console.log('⚠️ 发现错误状态的订阅，尝试重新更新');
                try {
                    await manager.updateSubscription(subscription.id);
                    console.log('✅ 订阅更新成功');
                } catch (error) {
                    console.error('❌ 订阅更新失败:', error.message);
                }
            }

            // 检查节点数量
            if (!subscription.nodes || subscription.nodes.length === 0) {
                console.log('⚠️ 发现无节点的订阅，尝试重新获取');
                try {
                    await manager.updateSubscription(subscription.id);
                    console.log('✅ 节点获取成功');
                } catch (error) {
                    console.error('❌ 节点获取失败:', error.message);
                }
            }

            // 检查更新时间
            if (!subscription.lastUpdate) {
                console.log('⚠️ 发现从未更新的订阅，尝试初始化');
                try {
                    await manager.updateSubscription(subscription.id);
                    console.log('✅ 订阅初始化成功');
                } catch (error) {
                    console.error('❌ 订阅初始化失败:', error.message);
                }
            }
        }

        // 刷新UI
        if (typeof updateProxyStatus === 'function') {
            updateProxyStatus();
        }
        if (typeof renderSubscriptionsList === 'function') {
            renderSubscriptionsList();
        }

        console.log('🎉 订阅问题修复完成');
    }

    /**
     * 创建测试订阅
     */
    async createTestSubscription() {
        const testContent = `# 测试订阅内容
http://proxy1.example.com:8080
https://proxy2.example.com:8443
socks5://proxy3.example.com:1080
proxy4.example.com:3128`;

        const dataUrl = 'data:text/plain;base64,' + btoa(testContent);
        
        console.log('🧪 创建测试订阅');
        return await this.testSubscriptionUrl(dataUrl, '测试订阅');
    }
}

// 创建全局实例
window.SubscriptionDebugger = new SubscriptionDebugger();

// 添加快捷函数
window.testSubscription = function(url, name) {
    return window.SubscriptionDebugger.testSubscriptionUrl(url, name);
};

window.testAllSubscriptions = function() {
    return window.SubscriptionDebugger.testAllSubscriptions();
};

window.fixSubscriptions = function() {
    return window.SubscriptionDebugger.fixSubscriptionIssues();
};

window.testDefaultSubscription = function() {
    return window.SubscriptionDebugger.testDefaultSubscription();
};

window.createTestSubscription = function() {
    return window.SubscriptionDebugger.createTestSubscription();
};
