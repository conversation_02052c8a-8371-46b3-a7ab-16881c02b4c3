/**
 * 代理订阅管理UI
 * 处理订阅管理界面的交互逻辑
 */

// 初始化订阅管理UI
function initProxySubscriptionUI() {
    if (!window.ProxySubscriptionManager) {
        console.error('ProxySubscriptionManager not found');
        return;
    }

    // 渲染订阅列表
    renderSubscriptionsList();
    
    // 更新代理状态显示
    updateProxyStatus();
    
    // 设置负载均衡开关状态
    const loadBalancingToggle = document.getElementById('loadBalancingToggle');
    if (loadBalancingToggle) {
        const isEnabled = window.ProxySubscriptionManager.loadBalancingEnabled;
        loadBalancingToggle.checked = isEnabled;

        // 更新开关样式
        updateToggleStyle(loadBalancingToggle, isEnabled);

        loadBalancingToggle.addEventListener('change', function() {
            const newState = this.checked;
            console.log('负载均衡开关状态变更:', newState);

            window.ProxySubscriptionManager.saveLoadBalancingEnabled(newState);
            updateToggleStyle(this, newState);
            updateProxyStatus();

            if (typeof showToast === 'function') {
                showToast(newState ? '已启用负载均衡' : '已禁用负载均衡', 'info');
            }
        });
    }

    // 定期更新状态显示
    setInterval(updateProxyStatus, 5000);
}

// 显示添加订阅表单
function showAddSubscriptionForm() {
    const form = document.getElementById('addSubscriptionForm');
    if (form) {
        form.classList.remove('hidden');
        
        // 如果是第一次添加，预填默认订阅URL
        const urlInput = document.getElementById('subscriptionUrl');
        if (urlInput && !urlInput.value && window.ProxySubscriptionManager.subscriptions.length === 0) {
            urlInput.value = PROXY_SUBSCRIPTION_CONFIG.defaultSubscriptionUrl;
        }
    }
}

// 取消添加订阅
function cancelAddSubscription() {
    const form = document.getElementById('addSubscriptionForm');
    if (form) {
        form.classList.add('hidden');
        
        // 清空表单
        document.getElementById('subscriptionName').value = '';
        document.getElementById('subscriptionUrl').value = '';
        document.getElementById('subscriptionAutoUpdate').checked = true;
    }
}

// 添加订阅
async function addSubscription() {
    const nameInput = document.getElementById('subscriptionName');
    const urlInput = document.getElementById('subscriptionUrl');
    const autoUpdateInput = document.getElementById('subscriptionAutoUpdate');
    
    const name = nameInput.value.trim();
    const url = urlInput.value.trim();
    const autoUpdate = autoUpdateInput.checked;
    
    if (!name) {
        showToast('请输入订阅名称', 'error');
        return;
    }
    
    if (!url) {
        showToast('请输入订阅URL', 'error');
        return;
    }
    
    // 验证URL格式
    try {
        new URL(url);
    } catch (error) {
        showToast('请输入有效的URL', 'error');
        return;
    }
    
    try {
        showToast('正在添加订阅...', 'info');
        
        await window.ProxySubscriptionManager.addSubscription(name, url, autoUpdate);
        
        showToast('订阅添加成功', 'success');
        cancelAddSubscription();
        renderSubscriptionsList();
        updateProxyStatus();
    } catch (error) {
        console.error('添加订阅失败:', error);
        showToast('添加订阅失败: ' + error.message, 'error');
    }
}

// 删除订阅
async function removeSubscription(subscriptionId) {
    if (!confirm('确定要删除这个订阅吗？')) {
        return;
    }
    
    try {
        const success = window.ProxySubscriptionManager.removeSubscription(subscriptionId);
        
        if (success) {
            showToast('订阅删除成功', 'success');
            renderSubscriptionsList();
            updateProxyStatus();
        } else {
            showToast('删除订阅失败', 'error');
        }
    } catch (error) {
        console.error('删除订阅失败:', error);
        showToast('删除订阅失败: ' + error.message, 'error');
    }
}

// 更新订阅
async function updateSubscription(subscriptionId) {
    if (!window.ProxySubscriptionManager) {
        showToast('代理管理器未初始化', 'error');
        return;
    }

    const subscription = window.ProxySubscriptionManager.subscriptions.find(sub => sub.id === subscriptionId);
    if (!subscription) {
        showToast('订阅不存在', 'error');
        return;
    }

    try {
        console.log('🔄 开始更新订阅:', subscription.name, subscription.url);
        showToast('正在更新订阅: ' + subscription.name, 'info');

        // 设置更新状态
        subscription.status = 'updating';
        renderSubscriptionsList();

        await window.ProxySubscriptionManager.updateSubscription(subscriptionId);

        console.log('✅ 订阅更新成功:', subscription.name);
        showToast('订阅更新成功: ' + subscription.name, 'success');

        renderSubscriptionsList();
        updateProxyStatus();

        // 显示节点数量
        const updatedSub = window.ProxySubscriptionManager.subscriptions.find(sub => sub.id === subscriptionId);
        if (updatedSub && updatedSub.nodes) {
            showToast(`获取到 ${updatedSub.nodes.length} 个节点`, 'info');
        }

    } catch (error) {
        console.error('❌ 更新订阅失败:', error);

        // 恢复错误状态
        if (subscription) {
            subscription.status = 'error';
        }

        renderSubscriptionsList();

        // 显示详细错误信息
        let errorMessage = error.message || '未知错误';
        if (errorMessage.includes('fetch')) {
            errorMessage = '网络请求失败，请检查订阅URL是否可访问';
        } else if (errorMessage.includes('parse')) {
            errorMessage = '订阅内容解析失败，请检查格式是否正确';
        }

        showToast('更新订阅失败: ' + errorMessage, 'error');

        // 提供调试建议
        console.log('💡 调试建议:');
        console.log('1. 在控制台运行: testSubscription("' + subscription.url + '", "' + subscription.name + '")');
        console.log('2. 检查订阅URL是否可访问');
        console.log('3. 运行: fixSubscriptions() 尝试自动修复');
    }
}

// 渲染订阅列表
function renderSubscriptionsList() {
    const container = document.getElementById('subscriptionsList');
    if (!container || !window.ProxySubscriptionManager) {
        return;
    }
    
    const subscriptions = window.ProxySubscriptionManager.subscriptions;
    
    if (subscriptions.length === 0) {
        container.innerHTML = '<div class="text-xs text-gray-500 text-center py-2">暂无订阅</div>';
        return;
    }
    
    let html = '';
    
    for (const subscription of subscriptions) {
        const statusClass = getSubscriptionStatusClass(subscription.status);
        const statusText = getSubscriptionStatusText(subscription.status);
        const nodeCount = subscription.nodes ? subscription.nodes.length : 0;
        const onlineCount = getOnlineNodeCount(subscription);
        
        html += `
            <div class="mb-2 p-2 bg-[#222] rounded border-l-2 ${statusClass}">
                <div class="flex justify-between items-start mb-1">
                    <div class="flex-1 min-w-0">
                        <div class="text-sm font-medium text-white truncate">${escapeHtml(subscription.name)}</div>
                        <div class="text-xs text-gray-400 truncate">${escapeHtml(subscription.url)}</div>
                    </div>
                    <div class="flex space-x-1 ml-2">
                        <button onclick="updateSubscription('${subscription.id}')" 
                                class="text-blue-400 hover:text-blue-300 text-xs px-1" 
                                title="更新订阅">
                            ↻
                        </button>
                        <button onclick="removeSubscription('${subscription.id}')" 
                                class="text-red-400 hover:text-red-300 text-xs px-1" 
                                title="删除订阅">
                            ×
                        </button>
                    </div>
                </div>
                <div class="flex justify-between items-center text-xs">
                    <span class="text-gray-500">${statusText}</span>
                    <span class="text-gray-400">${onlineCount}/${nodeCount} 节点</span>
                </div>
                ${subscription.lastUpdate ? `
                    <div class="text-xs text-gray-500 mt-1">
                        更新: ${formatDate(subscription.lastUpdate)}
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    container.innerHTML = html;
}

// 获取订阅状态样式类
function getSubscriptionStatusClass(status) {
    switch (status) {
        case 'active':
            return 'border-green-500';
        case 'updating':
            return 'border-yellow-500';
        case 'error':
            return 'border-red-500';
        case 'pending':
        default:
            return 'border-gray-500';
    }
}

// 获取订阅状态文本
function getSubscriptionStatusText(status) {
    switch (status) {
        case 'active':
            return '正常';
        case 'updating':
            return '更新中';
        case 'error':
            return '错误';
        case 'pending':
        default:
            return '等待中';
    }
}

// 获取在线节点数量
function getOnlineNodeCount(subscription) {
    if (!subscription.nodes || !window.ProxySubscriptionManager) {
        return 0;
    }
    
    return subscription.nodes.filter(node => {
        const latencyData = window.ProxySubscriptionManager.nodeLatencies[node.id];
        return latencyData && latencyData.status === 'online';
    }).length;
}

// 更新代理状态显示
function updateProxyStatus() {
    if (!window.ProxySubscriptionManager) {
        return;
    }
    
    const stats = window.ProxySubscriptionManager.getStats();
    
    // 更新代理状态
    const proxyStatus = document.getElementById('proxyStatus');
    if (proxyStatus) {
        proxyStatus.textContent = stats.proxyEnabled ? '启用' : '禁用';
        proxyStatus.className = stats.proxyEnabled ? 'text-xs text-green-400' : 'text-xs text-red-400';
    }
    
    // 更新在线节点数
    const onlineNodes = document.getElementById('onlineNodes');
    if (onlineNodes) {
        onlineNodes.textContent = `${stats.onlineNodes}/${stats.totalNodes}`;
    }
    
    // 更新当前节点
    const currentNode = document.getElementById('currentNode');
    if (currentNode) {
        const bestNode = stats.bestNode;
        if (bestNode) {
            const latency = window.ProxySubscriptionManager.nodeLatencies[bestNode.id]?.latency;
            const latencyText = latency ? ` (${latency}ms)` : '';
            currentNode.textContent = bestNode.name + latencyText;
            currentNode.className = 'text-xs text-green-400';
        } else {
            currentNode.textContent = stats.proxyEnabled ? '无可用节点' : '已禁用';
            currentNode.className = 'text-xs text-gray-400';
        }
    }
}

// 测试所有节点
async function testAllNodes() {
    if (!window.ProxySubscriptionManager) {
        return;
    }
    
    try {
        showToast('正在测试所有节点...', 'info');
        
        await window.ProxySubscriptionManager.testAllNodesLatency();
        
        showToast('节点测试完成', 'success');
        renderSubscriptionsList();
        updateProxyStatus();
    } catch (error) {
        console.error('测试节点失败:', error);
        showToast('测试节点失败: ' + error.message, 'error');
    }
}

// 刷新所有订阅
async function refreshSubscriptions() {
    if (!window.ProxySubscriptionManager) {
        return;
    }
    
    try {
        showToast('正在刷新订阅...', 'info');
        
        const updatePromises = window.ProxySubscriptionManager.subscriptions.map(sub => 
            window.ProxySubscriptionManager.updateSubscription(sub.id)
        );
        
        await Promise.allSettled(updatePromises);
        
        showToast('订阅刷新完成', 'success');
        renderSubscriptionsList();
        updateProxyStatus();
    } catch (error) {
        console.error('刷新订阅失败:', error);
        showToast('刷新订阅失败: ' + error.message, 'error');
    }
}

// 工具函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function updateToggleStyle(toggleInput, isChecked) {
    if (!toggleInput) return;

    // 查找相关的样式元素
    const toggleBg = toggleInput.parentElement.querySelector('.toggle-bg');
    const toggleDot = toggleInput.parentElement.querySelector('.toggle-dot');

    if (toggleBg) {
        if (isChecked) {
            toggleBg.classList.add('checked');
        } else {
            toggleBg.classList.remove('checked');
        }
    }

    if (toggleDot) {
        if (isChecked) {
            toggleDot.classList.add('checked');
        } else {
            toggleDot.classList.remove('checked');
        }
    }

    console.log('开关样式已更新:', isChecked, toggleBg, toggleDot);
}

function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '未知';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(initProxySubscriptionUI, 1000);
});

// 添加全局调试函数
window.debugProxy = function() {
    if (!window.ProxySubscriptionManager) {
        console.error('ProxySubscriptionManager not found');
        return;
    }

    const stats = window.ProxySubscriptionManager.getStats();
    console.log('=== 代理状态调试信息 ===');
    console.log('代理启用:', stats.proxyEnabled);
    console.log('负载均衡:', stats.loadBalancingEnabled);
    console.log('总订阅数:', stats.totalSubscriptions);
    console.log('总节点数:', stats.totalNodes);
    console.log('在线节点:', stats.onlineNodes);
    console.log('离线节点:', stats.offlineNodes);
    console.log('当前最佳节点:', stats.bestNode);
    console.log('订阅列表:', window.ProxySubscriptionManager.subscriptions);
    console.log('节点延迟:', window.ProxySubscriptionManager.nodeLatencies);

    return stats;
};
