<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅功能测试页面</title>
    <script src="libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center gradient-text">订阅功能测试页面</h1>
        
        <!-- 快速测试区域 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">快速测试</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testDefaultSub()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    测试默认订阅
                </button>
                <button onclick="createTestSub()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                    创建测试订阅
                </button>
                <button onclick="testAllSubs()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded">
                    测试所有订阅
                </button>
                <button onclick="fixAllSubs()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                    修复订阅问题
                </button>
            </div>
        </div>

        <!-- 自定义测试区域 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">自定义测试</h2>
            <div class="flex gap-2 mb-4">
                <input type="text" id="testUrl" placeholder="输入订阅URL" 
                       class="flex-1 bg-gray-700 border border-gray-600 px-3 py-2 rounded"
                       value="https://sub.0407123.xyz/admin?sub">
                <input type="text" id="testName" placeholder="订阅名称" 
                       class="w-48 bg-gray-700 border border-gray-600 px-3 py-2 rounded"
                       value="测试订阅">
                <button onclick="testCustomUrl()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    测试URL
                </button>
            </div>
            
            <!-- 预设URL -->
            <div class="mb-4">
                <label class="block text-sm text-gray-400 mb-2">预设测试URL:</label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <button onclick="setTestUrl('https://sub.0407123.xyz/admin?sub')" 
                            class="text-left bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm">
                        默认订阅: sub.0407123.xyz
                    </button>
                    <button onclick="setTestUrl('data:text/plain;base64,' + btoa('http://proxy1.example.com:8080\\nhttps://proxy2.example.com:8443\\nsocks5://proxy3.example.com:1080'))" 
                            class="text-left bg-gray-700 hover:bg-gray-600 px-3 py-2 rounded text-sm">
                        测试Data URL (3个节点)
                    </button>
                </div>
            </div>
        </div>

        <!-- 订阅状态显示 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">当前订阅状态</h2>
            <div id="subscriptionStatus" class="space-y-2">
                <div class="text-gray-400">加载中...</div>
            </div>
            <button onclick="refreshStatus()" class="mt-4 bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded">
                刷新状态
            </button>
        </div>

        <!-- 测试结果显示 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试结果</h2>
            <div id="testResults" class="space-y-4">
                <div class="text-gray-400">暂无测试结果</div>
            </div>
            <button onclick="clearResults()" class="mt-4 bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-sm">
                清除结果
            </button>
        </div>

        <!-- 控制台日志 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">控制台日志</h2>
            <div id="consoleLog" class="bg-gray-900 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                <div class="text-green-400">[INFO] 订阅测试页面已加载</div>
            </div>
            <div class="mt-2 flex gap-2">
                <button onclick="clearLog()" class="bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm">
                    清除日志
                </button>
                <button onclick="exportLog()" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                    导出日志
                </button>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="libs/sha256.min.js"></script>
    <script>window._jsSha256 = window.sha256;</script>
    <script src="js/config.js"></script>
    <script src="js/proxy-auth.js"></script>
    <script src="js/proxy-subscription.js"></script>
    <script src="js/proxy-subscription-ui.js"></script>
    <script src="js/subscription-debug.js"></script>

    <script>
        let logCount = 0;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const typeColors = {
                info: 'text-blue-400',
                success: 'text-green-400',
                warning: 'text-yellow-400',
                error: 'text-red-400'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = typeColors[type] || 'text-gray-400';
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            logCount++;
            if (logCount > 200) {
                logElement.removeChild(logElement.firstChild);
                logCount--;
            }
        }

        function clearLog() {
            document.getElementById('consoleLog').innerHTML = '';
            logCount = 0;
            log('日志已清除', 'info');
        }

        function exportLog() {
            const logContent = document.getElementById('consoleLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `subscription-test-log-${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 重写console方法以捕获日志
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            log(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            log(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            log(args.join(' '), 'warning');
        };

        // 测试函数
        async function testDefaultSub() {
            log('开始测试默认订阅', 'info');
            try {
                const result = await testDefaultSubscription();
                displayTestResult(result);
            } catch (error) {
                log('测试默认订阅失败: ' + error.message, 'error');
            }
        }

        async function createTestSub() {
            log('创建测试订阅', 'info');
            try {
                const result = await createTestSubscription();
                displayTestResult(result);
            } catch (error) {
                log('创建测试订阅失败: ' + error.message, 'error');
            }
        }

        async function testAllSubs() {
            log('测试所有订阅', 'info');
            try {
                const results = await testAllSubscriptions();
                results.forEach(result => displayTestResult(result));
            } catch (error) {
                log('测试所有订阅失败: ' + error.message, 'error');
            }
        }

        async function fixAllSubs() {
            log('修复所有订阅问题', 'info');
            try {
                await fixSubscriptions();
                refreshStatus();
            } catch (error) {
                log('修复订阅失败: ' + error.message, 'error');
            }
        }

        async function testCustomUrl() {
            const url = document.getElementById('testUrl').value.trim();
            const name = document.getElementById('testName').value.trim() || '自定义测试';
            
            if (!url) {
                log('请输入订阅URL', 'warning');
                return;
            }

            log(`测试自定义URL: ${url}`, 'info');
            try {
                const result = await testSubscription(url, name);
                displayTestResult(result);
            } catch (error) {
                log('测试自定义URL失败: ' + error.message, 'error');
            }
        }

        function setTestUrl(url) {
            document.getElementById('testUrl').value = url;
            log('已设置测试URL', 'info');
        }

        function displayTestResult(result) {
            const resultsContainer = document.getElementById('testResults');
            
            const resultDiv = document.createElement('div');
            resultDiv.className = 'bg-gray-700 p-4 rounded';
            
            const statusColor = result.success ? 'text-green-400' : 'text-red-400';
            const statusIcon = result.success ? '✅' : '❌';
            
            resultDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-semibold">${result.name}</h3>
                    <span class="${statusColor}">${statusIcon} ${result.success ? '成功' : '失败'}</span>
                </div>
                <div class="text-sm text-gray-400 mb-2">${result.url}</div>
                <div class="text-sm">
                    <span class="text-blue-400">节点数:</span> ${result.nodes.length}
                    <span class="ml-4 text-blue-400">时间:</span> ${new Date(result.timestamp).toLocaleTimeString()}
                </div>
                ${result.error ? `<div class="text-red-400 text-sm mt-2">错误: ${result.error}</div>` : ''}
                ${result.nodes.length > 0 ? `
                    <div class="mt-2">
                        <details class="text-sm">
                            <summary class="cursor-pointer text-blue-400">节点详情 (${result.nodes.length})</summary>
                            <div class="mt-2 space-y-1">
                                ${result.nodes.map((node, index) => 
                                    `<div class="text-gray-300">${index + 1}. ${node.name} (${node.protocol})</div>`
                                ).join('')}
                            </div>
                        </details>
                    </div>
                ` : ''}
            `;
            
            // 插入到顶部
            if (resultsContainer.firstChild && resultsContainer.firstChild.textContent.includes('暂无测试结果')) {
                resultsContainer.innerHTML = '';
            }
            resultsContainer.insertBefore(resultDiv, resultsContainer.firstChild);
        }

        function refreshStatus() {
            const statusContainer = document.getElementById('subscriptionStatus');
            
            if (!window.ProxySubscriptionManager) {
                statusContainer.innerHTML = '<div class="text-red-400">ProxySubscriptionManager 未初始化</div>';
                return;
            }

            const manager = window.ProxySubscriptionManager;
            const subscriptions = manager.subscriptions;
            const stats = manager.getStats();

            let html = `
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">订阅数量</div>
                        <div class="text-lg font-semibold">${stats.totalSubscriptions}</div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">总节点数</div>
                        <div class="text-lg font-semibold">${stats.totalNodes}</div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">在线节点</div>
                        <div class="text-lg font-semibold text-green-400">${stats.onlineNodes}</div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="text-sm text-gray-400">代理状态</div>
                        <div class="text-lg font-semibold ${stats.proxyEnabled ? 'text-green-400' : 'text-red-400'}">
                            ${stats.proxyEnabled ? '启用' : '禁用'}
                        </div>
                    </div>
                </div>
            `;

            if (subscriptions.length > 0) {
                html += '<div class="space-y-2">';
                subscriptions.forEach((sub, index) => {
                    const statusColors = {
                        active: 'text-green-400',
                        updating: 'text-yellow-400',
                        error: 'text-red-400',
                        pending: 'text-gray-400'
                    };
                    
                    html += `
                        <div class="bg-gray-700 p-3 rounded flex justify-between items-center">
                            <div>
                                <div class="font-medium">${sub.name}</div>
                                <div class="text-sm text-gray-400">${sub.url}</div>
                                <div class="text-xs ${statusColors[sub.status] || 'text-gray-400'}">
                                    ${sub.status} - ${sub.nodes ? sub.nodes.length : 0} 节点
                                    ${sub.lastUpdate ? ' - ' + new Date(sub.lastUpdate).toLocaleString() : ''}
                                </div>
                            </div>
                            <button onclick="testSubscriptionById('${sub.id}')" 
                                    class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm">
                                测试
                            </button>
                        </div>
                    `;
                });
                html += '</div>';
            } else {
                html += '<div class="text-gray-400">暂无订阅</div>';
            }

            statusContainer.innerHTML = html;
        }

        async function testSubscriptionById(subscriptionId) {
            const manager = window.ProxySubscriptionManager;
            const subscription = manager.subscriptions.find(sub => sub.id === subscriptionId);
            
            if (!subscription) {
                log('订阅不存在', 'error');
                return;
            }

            log(`测试订阅: ${subscription.name}`, 'info');
            try {
                const result = await testSubscription(subscription.url, subscription.name);
                displayTestResult(result);
            } catch (error) {
                log(`测试订阅失败: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<div class="text-gray-400">暂无测试结果</div>';
            log('测试结果已清除', 'info');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('订阅测试页面初始化', 'info');
            
            setTimeout(() => {
                if (window.ProxySubscriptionManager) {
                    log('ProxySubscriptionManager 已初始化', 'success');
                    refreshStatus();
                    
                    // 定期刷新状态
                    setInterval(refreshStatus, 10000);
                } else {
                    log('ProxySubscriptionManager 未初始化', 'error');
                }
            }, 2000);
        });
    </script>
</body>
</html>
