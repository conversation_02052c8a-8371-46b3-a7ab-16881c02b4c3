<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LibreTV 代理功能演示</title>
    <script src="libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center gradient-text">LibreTV 代理功能演示</h1>
        
        <!-- 代理状态面板 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">代理状态</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-gray-700 p-3 rounded">
                    <div class="text-sm text-gray-400">代理状态</div>
                    <div id="demo-proxy-status" class="text-lg font-semibold text-green-400">启用</div>
                </div>
                <div class="bg-gray-700 p-3 rounded">
                    <div class="text-sm text-gray-400">在线节点</div>
                    <div id="demo-online-nodes" class="text-lg font-semibold">0/0</div>
                </div>
                <div class="bg-gray-700 p-3 rounded">
                    <div class="text-sm text-gray-400">当前节点</div>
                    <div id="demo-current-node" class="text-lg font-semibold text-blue-400">自动选择</div>
                </div>
                <div class="bg-gray-700 p-3 rounded">
                    <div class="text-sm text-gray-400">负载均衡</div>
                    <div id="demo-load-balancing" class="text-lg font-semibold text-yellow-400">启用</div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">控制面板</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="toggleProxyDemo()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    切换代理状态
                </button>
                <button onclick="toggleLoadBalancingDemo()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                    切换负载均衡
                </button>
                <button onclick="testAllNodesDemo()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded">
                    测试所有节点
                </button>
                <button onclick="refreshSubscriptionsDemo()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    刷新订阅
                </button>
            </div>
        </div>

        <!-- 订阅管理 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">订阅管理</h2>
            <div class="mb-4">
                <div class="flex gap-2 mb-2">
                    <input type="text" id="demo-sub-name" placeholder="订阅名称" class="flex-1 bg-gray-700 border border-gray-600 px-3 py-2 rounded">
                    <input type="text" id="demo-sub-url" placeholder="订阅URL" class="flex-1 bg-gray-700 border border-gray-600 px-3 py-2 rounded">
                    <button onclick="addSubscriptionDemo()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">添加</button>
                </div>
            </div>
            <div id="demo-subscriptions-list" class="space-y-2">
                <!-- 订阅列表将显示在这里 -->
            </div>
        </div>

        <!-- 测试面板 -->
        <div class="bg-gray-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">功能测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="runProxyTests()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                    运行完整测试套件
                </button>
                <button onclick="debugProxy()" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded">
                    输出调试信息
                </button>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">操作日志</h2>
            <div id="demo-log" class="bg-gray-900 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                <div class="text-green-400">[INFO] 代理演示页面已加载</div>
            </div>
            <button onclick="clearLog()" class="mt-2 bg-gray-600 hover:bg-gray-700 px-3 py-1 rounded text-sm">
                清除日志
            </button>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="libs/sha256.min.js"></script>
    <script>
        // 保存原始 js‑sha256 实现
        window._jsSha256 = window.sha256;
    </script>
    <script src="js/config.js"></script>
    <script src="js/proxy-auth.js"></script>
    <script src="js/proxy-subscription.js"></script>
    <script src="js/proxy-subscription-ui.js"></script>
    <script src="js/proxy-test.js"></script>

    <script>
        // 演示页面专用函数
        let logCount = 0;

        function log(message, type = 'info') {
            const logElement = document.getElementById('demo-log');
            const timestamp = new Date().toLocaleTimeString();
            const typeColors = {
                info: 'text-blue-400',
                success: 'text-green-400',
                warning: 'text-yellow-400',
                error: 'text-red-400'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = typeColors[type] || 'text-gray-400';
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            logCount++;
            if (logCount > 100) {
                // 保持日志数量在合理范围内
                logElement.removeChild(logElement.firstChild);
                logCount--;
            }
        }

        function clearLog() {
            document.getElementById('demo-log').innerHTML = '';
            logCount = 0;
            log('日志已清除', 'info');
        }

        function updateDemoStatus() {
            if (!window.ProxySubscriptionManager) {
                return;
            }

            const stats = window.ProxySubscriptionManager.getStats();
            
            document.getElementById('demo-proxy-status').textContent = stats.proxyEnabled ? '启用' : '禁用';
            document.getElementById('demo-proxy-status').className = stats.proxyEnabled ? 'text-lg font-semibold text-green-400' : 'text-lg font-semibold text-red-400';
            
            document.getElementById('demo-online-nodes').textContent = `${stats.onlineNodes}/${stats.totalNodes}`;
            
            const currentNodeEl = document.getElementById('demo-current-node');
            if (stats.bestNode) {
                const latency = window.ProxySubscriptionManager.nodeLatencies[stats.bestNode.id]?.latency;
                const latencyText = latency ? ` (${latency}ms)` : '';
                currentNodeEl.textContent = stats.bestNode.name + latencyText;
                currentNodeEl.className = 'text-lg font-semibold text-green-400';
            } else {
                currentNodeEl.textContent = stats.proxyEnabled ? '无可用节点' : '已禁用';
                currentNodeEl.className = 'text-lg font-semibold text-gray-400';
            }
            
            document.getElementById('demo-load-balancing').textContent = stats.loadBalancingEnabled ? '启用' : '禁用';
            document.getElementById('demo-load-balancing').className = stats.loadBalancingEnabled ? 'text-lg font-semibold text-yellow-400' : 'text-lg font-semibold text-gray-400';
        }

        function renderDemoSubscriptions() {
            if (!window.ProxySubscriptionManager) {
                return;
            }

            const container = document.getElementById('demo-subscriptions-list');
            const subscriptions = window.ProxySubscriptionManager.subscriptions;
            
            if (subscriptions.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无订阅</div>';
                return;
            }
            
            let html = '';
            for (const sub of subscriptions) {
                const statusColors = {
                    active: 'text-green-400',
                    updating: 'text-yellow-400',
                    error: 'text-red-400',
                    pending: 'text-gray-400'
                };
                
                html += `
                    <div class="bg-gray-700 p-3 rounded flex justify-between items-center">
                        <div>
                            <div class="font-semibold">${sub.name}</div>
                            <div class="text-sm text-gray-400">${sub.url}</div>
                            <div class="text-xs ${statusColors[sub.status] || 'text-gray-400'}">${sub.status} - ${sub.nodes ? sub.nodes.length : 0} 节点</div>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="updateSubscriptionDemo('${sub.id}')" class="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs">更新</button>
                            <button onclick="removeSubscriptionDemo('${sub.id}')" class="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs">删除</button>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // 演示控制函数
        function toggleProxyDemo() {
            if (!window.ProxySubscriptionManager) return;
            
            const newState = !window.ProxySubscriptionManager.proxyEnabled;
            window.ProxySubscriptionManager.saveProxyEnabled(newState);
            log(`代理已${newState ? '启用' : '禁用'}`, newState ? 'success' : 'warning');
            updateDemoStatus();
        }

        function toggleLoadBalancingDemo() {
            if (!window.ProxySubscriptionManager) return;
            
            const newState = !window.ProxySubscriptionManager.loadBalancingEnabled;
            window.ProxySubscriptionManager.saveLoadBalancingEnabled(newState);
            log(`负载均衡已${newState ? '启用' : '禁用'}`, newState ? 'success' : 'warning');
            updateDemoStatus();
        }

        async function testAllNodesDemo() {
            if (!window.ProxySubscriptionManager) return;
            
            log('开始测试所有节点...', 'info');
            try {
                await window.ProxySubscriptionManager.testAllNodesLatency();
                log('节点测试完成', 'success');
                updateDemoStatus();
                renderDemoSubscriptions();
            } catch (error) {
                log('节点测试失败: ' + error.message, 'error');
            }
        }

        async function refreshSubscriptionsDemo() {
            if (!window.ProxySubscriptionManager) return;
            
            log('开始刷新订阅...', 'info');
            try {
                const updatePromises = window.ProxySubscriptionManager.subscriptions.map(sub => 
                    window.ProxySubscriptionManager.updateSubscription(sub.id)
                );
                await Promise.allSettled(updatePromises);
                log('订阅刷新完成', 'success');
                updateDemoStatus();
                renderDemoSubscriptions();
            } catch (error) {
                log('刷新订阅失败: ' + error.message, 'error');
            }
        }

        async function addSubscriptionDemo() {
            if (!window.ProxySubscriptionManager) return;
            
            const name = document.getElementById('demo-sub-name').value.trim();
            const url = document.getElementById('demo-sub-url').value.trim();
            
            if (!name || !url) {
                log('请输入订阅名称和URL', 'warning');
                return;
            }
            
            try {
                log(`添加订阅: ${name}`, 'info');
                await window.ProxySubscriptionManager.addSubscription(name, url, true);
                log('订阅添加成功', 'success');
                
                // 清空输入框
                document.getElementById('demo-sub-name').value = '';
                document.getElementById('demo-sub-url').value = '';
                
                updateDemoStatus();
                renderDemoSubscriptions();
            } catch (error) {
                log('添加订阅失败: ' + error.message, 'error');
            }
        }

        async function updateSubscriptionDemo(subscriptionId) {
            if (!window.ProxySubscriptionManager) return;
            
            try {
                log('更新订阅...', 'info');
                await window.ProxySubscriptionManager.updateSubscription(subscriptionId);
                log('订阅更新成功', 'success');
                updateDemoStatus();
                renderDemoSubscriptions();
            } catch (error) {
                log('更新订阅失败: ' + error.message, 'error');
            }
        }

        function removeSubscriptionDemo(subscriptionId) {
            if (!window.ProxySubscriptionManager) return;
            
            if (!confirm('确定要删除这个订阅吗？')) {
                return;
            }
            
            try {
                const success = window.ProxySubscriptionManager.removeSubscription(subscriptionId);
                if (success) {
                    log('订阅删除成功', 'success');
                    updateDemoStatus();
                    renderDemoSubscriptions();
                } else {
                    log('删除订阅失败', 'error');
                }
            } catch (error) {
                log('删除订阅失败: ' + error.message, 'error');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('初始化代理演示页面...', 'info');
            
            // 等待代理管理器初始化
            setTimeout(() => {
                if (window.ProxySubscriptionManager) {
                    log('代理管理器已初始化', 'success');
                    updateDemoStatus();
                    renderDemoSubscriptions();
                    
                    // 定期更新状态
                    setInterval(() => {
                        updateDemoStatus();
                        renderDemoSubscriptions();
                    }, 5000);
                } else {
                    log('代理管理器初始化失败', 'error');
                }
            }, 2000);
        });
    </script>
</body>
</html>
